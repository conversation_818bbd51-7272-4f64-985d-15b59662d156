#!/bin/bash

# 焦点监听脚本
# 用法: ./focus_monitor.sh [选项]
# 选项:
#   -c, --continuous    持续监听焦点变化
#   -s, --spy          使用 xprop spy 模式监听
#   -o, --once         只显示当前焦点窗口信息
#   -h, --help         显示帮助信息

show_help() {
    echo "焦点监听工具"
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -c, --continuous    持续监听焦点变化 (每秒检查一次)"
    echo "  -s, --spy          使用 xprop spy 模式监听属性变化"
    echo "  -o, --once         只显示当前焦点窗口信息"
    echo "  -h, --help         显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 -c              # 持续监听焦点变化"
    echo "  $0 -s              # 使用 spy 模式监听"
    echo "  $0 -o              # 显示当前焦点窗口"
}

get_window_info() {
    local window_id=$1
    local window_name=$(xdotool getwindowname $window_id 2>/dev/null || echo "Unknown")
    local window_class=$(xprop -id $window_id WM_CLASS 2>/dev/null | cut -d'"' -f4 2>/dev/null || echo "Unknown")
    local window_pid=$(xprop -id $window_id _NET_WM_PID 2>/dev/null | cut -d'=' -f2 | tr -d ' ' || echo "Unknown")
    
    echo "时间: $(date '+%Y-%m-%d %H:%M:%S')"
    echo "窗口ID: $window_id"
    echo "窗口名称: $window_name"
    echo "窗口类: $window_class"
    echo "进程PID: $window_pid"
    echo "----------------------------------------"
}

continuous_monitor() {
    echo "开始持续监听焦点变化 (按 Ctrl+C 停止)..."
    echo "========================================"
    
    local last_window_id=""
    
    while true; do
        local current_window_id=$(xdotool getwindowfocus 2>/dev/null)
        
        if [ "$current_window_id" != "$last_window_id" ]; then
            echo "焦点变化检测到!"
            get_window_info "$current_window_id"
            last_window_id="$current_window_id"
        fi
        
        sleep 1
    done
}

spy_monitor() {
    echo "使用 xprop spy 模式监听焦点变化 (按 Ctrl+C 停止)..."
    echo "================================================"
    
    xprop -root -spy _NET_ACTIVE_WINDOW | while read line; do
        if [[ $line =~ window\ id\ #\ ([0-9a-fx]+) ]]; then
            local window_id="${BASH_REMATCH[1]}"
            echo "检测到焦点变化: $line"
            get_window_info "$window_id"
        fi
    done
}

show_current() {
    echo "当前焦点窗口信息:"
    echo "=================="
    local current_window_id=$(xdotool getwindowfocus 2>/dev/null)
    get_window_info "$current_window_id"
}

# 检查依赖
if ! command -v xdotool &> /dev/null; then
    echo "错误: 需要安装 xdotool"
    echo "Ubuntu/Debian: sudo apt install xdotool"
    echo "CentOS/RHEL: sudo yum install xdotool"
    exit 1
fi

if ! command -v xprop &> /dev/null; then
    echo "错误: 需要安装 xprop (通常包含在 x11-utils 中)"
    exit 1
fi

# 解析命令行参数
case "${1:-}" in
    -c|--continuous)
        continuous_monitor
        ;;
    -s|--spy)
        spy_monitor
        ;;
    -o|--once)
        show_current
        ;;
    -h|--help)
        show_help
        ;;
    "")
        show_current
        ;;
    *)
        echo "未知选项: $1"
        show_help
        exit 1
        ;;
esac
