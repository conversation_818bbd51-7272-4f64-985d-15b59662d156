dde-shell (2.0.3) unstable; urgency=medium

  * feat: enhance notification validation and cleanup
  * refactor: remove debug panel and legacy DBus code
  * refactor: update notification handling and data access logic
  * fix: sort notifications by timestamp before display
  * i18n: Updates for project Deepin Desktop Environment (#1174)
  * chore: reduce layershell emulate log message's log level

 -- WuJiangYu <<EMAIL>>  Thu, 10 Jul 2025 20:15:01 +0800

dde-shell (2.0.2) unstable; urgency=medium

  * fix: fix safe build.
  * fix: cannot build on Archlinux
  * fix: Fixed the blurriness of the preview window to separate the X11
    and wayland environments.
  * fix: fix launchpad fullsream, click dock ,it will not close.
  * fix: init showingDesktop for smart Hide.
  * feat: add preview follows the system opacity.
  * fix: modify when keep hiden animation has launched, has shadow and
    line.
  * fix: adapt import change of QtQml.Models in Qt 6.9
  * i18n: Updates for project Deepin Desktop Environment (#1172)
  * fix(tray): allow plugin icons to be dragged to leftmost position ﻿ -
    Fix overly strict drag condition in TrayContainer.qml that prevented
    plugin icons from being dragged to the leftmost position - Replace
    `dropHoverIndex !== 0` check with precise condition `dropHoverIndex
    === 0 && !surfaceId.startsWith("application-tray") && isBefore` -
    Maintain protection for application tray icons while allowing plugin
    icons to be properly positioned - Fix inconsistent behavior between
    animation states

 -- WuJiangYu <<EMAIL>>  Thu, 03 Jul 2025 16:32:57 +0800

dde-shell (2.0.1) unstable; urgency=medium

  * chore: remove invalid translation resources
  * i18n: Updates for project Deepin Desktop Environment (#1168)
  * fix: incorrect target ts file path in transifex.yaml
  * i18n: Updates for project Deepin Desktop Environment (#1167)
  * fix: add missing resource in .tx/transifex.yaml

 -- WuJiangYu <<EMAIL>>  Tue, 24 Jun 2025 10:40:41 +0800

dde-shell (2.0.0) unstable; urgency=medium

  * i18n: Updates for project Deepin Desktop Environment (#1162)
  * chore: adjust dockedElements DConfig
  * feat: add smart hide when hide desktop.
  * i18n: Updates for project Deepin Desktop Environment (#1159)
  * chore: set libdde-shell.so SOVERSION to 1

 -- YeShanShan <<EMAIL>>  Thu, 19 Jun 2025 10:15:26 +0800

dde-shell (1.99.39) unstable; urgency=medium

  * chore: use ICU for relative datetime formatting
  * fix: layershell calcular in x11 emulation
  * fix: The tray application flashes an icon when opened
  * feat: add activeWindow D-Bus interface
  * chore: tweak role{group,combine}model

-- Wang Zichong <<EMAIL>>  Thu, 12 Jun 2025 19:12:00 +0800

dde-shell (1.99.38) unstable; urgency=medium

  * fix: The tray window panel automatically opens 
  * fix: just use one Dbus interface.
  * fix: update tanslation with Lock dock. 
  * feat: Lock the height of dock. 

-- Wu Jiangyu <<EMAIL>>  Thu, 05 Jun 2025 16:35:14 +0800

dde-shell (1.99.37) unstable; urgency=medium

  * fix: support string list in notification hints
  * fix: replace NumberAnimation with XAnimator in notification bubble
  * style: enhance OSD window visual effects
  * fix: remove redundant mode property in NotifyItemContent
  * chore: simplify window effect descriptions
  * fix: incorrectly removed window in AbstractWindowMonitor
  * fix: add missing winIconRole to DockCombineModel
  * fix: smart hide state not updated when window closed
  * i18n: Updates for project Deepin Desktop Environment (#1127)
  * refactor: prepare for combined model for taskmanager

 -- YeShanShan <<EMAIL>>  Wed, 28 May 2025 18:30:25 +0800

dde-shell (1.99.36) unstable; urgency=medium

  * feat: introduce RoleGroupModel
  * refactor: update display mode icons and logic

 -- YeShanShan <<EMAIL>>  Thu, 15 May 2025 21:15:21 +0800

dde-shell (1.99.35) unstable; urgency=medium

  * chore: drop no longer existed translation resource 
  * chore: update copywriting of dock modes 
  * feat: Add animation for position switching to dock	
  * Update translations from Transifex

-- Wu Jiangyu <<EMAIL>>  Tue, 13 May 2025 17:20:000 +0800

dde-shell (1.99.34) unstable; urgency=medium

  * refactor: simplify notification action handling
  * fix: need request back when embed popup hidden
  * fix: prevent duplicate OSD actions for same type
  * fix: remove DSG_APP_ID from notification action
  * chore: release dde-shell 1.99.33

 -- YeShanShan <<EMAIL>>  Thu, 08 May 2025 17:41:17 +0800

dde-shell (1.99.33) unstable; urgency=medium

  * [skip CI] Translate org.deepin.ds.osd.default.ts in pl
  * [skip CI] Translate org.deepin.ds.notificationcenter.ts in pt_BR
  * fix: Fixed the issue that qCDebug does not output
  * fix: initialize dock color theme with default value
  * fix: adjust ui of title for notification
  * fix: [Font-size] The Font size can not change by dde-control-center. (#1105)

 -- YeShanShan <<EMAIL>>  Tue, 29 Apr 2025 14:39:30 +0800

dde-shell (1.99.32) UNRELEASED; urgency=medium

  * chore: missing some translations for notification (BUG-289337)
  * fix: compatibility with Qt 6.9+
  * fix: elements not aligned correctly when dock resize (BUG-312831)
  * fix: need update exclusion zone when screen was added or removed
    (BUG-306339)
  * fix: bluetooth still entered notificationcenter (BUG-311617)
  * fix: The icons residing on pannel move from the right side to the left side
    (BUG-309403)
  * fix: dnd does not disable to play sound for notification (BUG-310921)

 -- Wang Zichong <<EMAIL>>  Thu, 17 Apr 2025 17:46:00 +0800

dde-shell (1.99.31) UNRELEASED; urgency=medium

  * fix: dock plugin display blurred on 1.25 screen scale ratio
  * refactor: Remove `searchitem` module from dock

 -- zhaoyingzhen <<EMAIL>>  Thu, 10 Apr 2025 17:36:25 +0800

dde-shell (1.99.30) UNRELEASED; urgency=medium

  * bump version to 1.99.30

 -- zhangkun <<EMAIL>>  Thu, 27 Mar 2025 16:48:29 +0800

dde-shell (1.99.29) UNRELEASED; urgency=medium

  * i18n: Updates for project Deepin Desktop Environment (#1064)
    Thanks to transifex-integration[bot]
  * feat: Receive close quick panel requests from plugins(Bug: 272277)
  * feat: adjust OSD ui according to v25
  * fix: shadow clipped for notification
  * fix: window preview blurry

 -- YeShanShan <<EMAIL>>  Thu, 27 Mar 2025 16:48:29 +0800

dde-shell (1.99.28) UNRELEASED; urgency=medium

  * chore: update translation

 -- zhangkun <<EMAIL>>  Fri, 14 Mar 2025 11:07:05 +0800

dde-shell (1.99.27) UNRELEASED; urgency=medium

  * fix: task area might have incorrect height after resize in some cases
    (BUG: 303541, 303155)

 --  Wang Zichong <<EMAIL>>  Thu, 13 Mar 2025 15:13:00 +0800

dde-shell (1.99.26) UNRELEASED; urgency=medium

  * fix: num-lock can't enable

 -- YeShanShan <<EMAIL>>  Wed, 12 Mar 2025 14:50:53 +0800

dde-shell (1.99.25) UNRELEASED; urgency=medium

  * chore: bump version to 1.99.25

 -- Deepin Packages Builder <<EMAIL>>  Thu, 06 Mar 2025 19:58:56 +0800

dde-shell (1.99.24) UNRELEASED; urgency=medium

  * chore: bump version to 1.99.24

 -- Deepin Packages Builder <<EMAIL>>  Fri, 28 Feb 2025 10:25:56 +0800

dde-shell (1.99.23) UNRELEASED; urgency=medium

  * chore: remove dcc-dock-plugin

 -- zhangkun <<EMAIL>>  Fri, 21 Feb 2025 18:17:08 +0800

dde-shell (1.99.22) UNRELEASED; urgency=medium

  * fix: record replaced notification to DB (Bug: 296235)
  * fix: Error with SmartHidden and KeepHidden modes of dock (Bug: 302819)
  * feat: drop to dock task icon to launch with uri (Bug: 275395)
  * fix: Staging notifications cannot disappear after timeout (Bug: 303331)
  * fix: Incorrect application layout menu display content (Bug: 289221, 286541, 286553)
  * fix: Notifications not displayed in the center cannot disappear after timeout (Bug: 303295)
  * fix: Staging notifications cannot disappear after timeout (Bug: 303331)
  * fix: Dragging an application will hide the dock (Bug: 289207, 289205)
  * fix: add offset to quick panel drag item to avoid cursor cover icon (Bug: 293867)
  * fix: The dock automatically hides
  * fix: item size not updated when taskbar item count was changed (Bug: 289225)
  * i18n: Updates for project Deepin Desktop Environment
 
 -- Wang Zichong <<EMAIL>>  Fri, 21 Feb 2025 13:42:00 +0800

dde-shell (1.99.21) UNRELEASED; urgency=medium

  * fix: update replace notification entity expired time(Bug: 296507)
  * fix: pinned application, notifications display order is incorrect(Bug: 299887)

 -- Deepin Packages Builder <<EMAIL>>  Fri, 14 Feb 2025 10:24:56 +0800

dde-shell (1.99.20) UNRELEASED; urgency=medium

  * feat: provide execs and isLingLong properties
  * fix: jittering when draggin quick action applet
  * feat: Add files generated by qdbusXML2cpp and DCONG2cpp

 -- zhangkun <<EMAIL>>  Thu, 23 Jan 2025 17:35:43 +0800

dde-shell (1.99.19) UNRELEASED; urgency=medium

  * Temporary disable grand search icon for treeland
  * Fix access wild pointer in notification center

 -- Wang Zichong <<EMAIL>>  Tue, 14 Jan 2025 16:00:00 +0800

dde-shell (1.99.18) UNRELEASED; urgency=medium

  * fix: plugin docked into tray area (#1007)
  * fix: drop BoxShadow for Attention animation
  * chore: Add the qt6-wayland running dependency

 -- Deepin Packages Builder <<EMAIL>>  Thu, 09 Jan 2025 20:34:48 +0800

dde-shell (1.99.17) UNRELEASED; urgency=medium

  * UNRELEASED

 -- Deepin Packages Builder <<EMAIL>>  Fri, 03 Jan 2025 13:29:38 +0800

dde-shell (1.99.16) UNRELEASED; urgency=medium

  * fix: notification-center auto hide when clicked other window as title(Bug: 294647)

 -- Deepin Packages Builder <<EMAIL>>  Tue, 24 Dec 2024 20:35:07 +0800

dde-shell (1.99.15) UNRELEASED; urgency=medium

  * fix: Video memory explosion caused by randomly clicking on dock plugins
  * chore: update section to shells in debian/control

 -- Deepin Packages Builder <<EMAIL>>  Mon, 23 Dec 2024 15:30:15 +0800

dde-shell (1.99.14) UNRELEASED; urgency=medium

  * fix: incorrect margin in treeland for notification
  * fix: error max height of embed plugin popup
  * fix: can't query Notify for notification
  * fix: missing default action for shutdown

 -- YeShanShan <<EMAIL>>  Mon, 23 Dec 2024 10:49:38 +0800

dde-shell (1.99.13) UNRELEASED; urgency=medium

  * fix: Remove position change timer on X11Emulation
  * fix: Shutdown plugin miss menu function(Bug: 294875)

 -- Deepin Packages Builder <<EMAIL>>  Thu, 19 Dec 2024 10:24:00 +0800

dde-shell (1.99.12) UNRELEASED; urgency=medium

  * UNRELEASED bump version to 1.99.12

 -- Deepin Packages Builder <<EMAIL>>  Sat, 14 Dec 2024 14:46:32 +0800

dde-shell (1.99.11) UNRELEASED; urgency=medium

  * fix: popup can not hide while click twice on treeland
  * fix: action can't work
  * fix: adjust ui for notification
  * fix: windoweffect osd triggered abnormally

 -- tsic404 <<EMAIL>>  Mon, 09 Dec 2024 13:39:32 +0800

dde-shell (1.99.10) UNRELEASED; urgency=medium

  * feat: Auto hide dde-dock on window fullscreen in Wayland(BUG: 263325, 278961)
  * fix: Modifying notifications number, old notifications are displayed after new notifications are added.(Bug: 290209)
  * fix: Setting notifications number less than 3, do not display overlay(Bug: 290185)
  * fix: Closing notification when it was closed or invoked from notification-center.(Bug: 290009)
  * fix: Modify 'dndMode' and 'openByTimeInterval' setting default value(Bug: 289603)
  * fix: Disable dropping of already displayed plugin.(Bug: 266651)
  * chore: Modify professional os taskManager fixed application(Bug: 266869)
  * chore: TrayLoader single process support to start multiple plugins(Bug: 283901)

 -- Deepin Packages Builder <<EMAIL>>  Fri, 06 Dec 2024 15:19:20 +0800

dde-shell (1.99.9) UNRELEASED; urgency=medium

  * fix popup can not hide on treeland

 -- tsic404 <<EMAIL>>  Fri, 29 Nov 2024 17:30:18 +0800

dde-shell (1.99.8) UNRELEASED; urgency=medium

  * fix: Adjust the effects level of kwin to Optimal-performance, multiTasks plugin can not hide(Bug: 286871)
  * fix: Display bubble icon normally(Bug: 286813)

 -- Deepin Packages Builder <<EMAIL>>  Fri, 29 Nov 2024 11:14:42 +0800

dde-shell (1.99.7) UNRELEASED; urgency=medium

  * fix: can't response action for notification(Bug: 284861)
  * fix: incomplete display of time for notification(Bug: 283509)
  * fix: error displayed in stage for notification(Bug: 283969)
  * fix: empty string can't represent AllApp for notification(Task: 366403)
  * fix: change notification's layer for notification(Bug: 284425)
  * fix: incorrect height of notificationCenter for notification(Bug: 283749)
  * refact: extract notifyItem content for notification(Task: 366403)
  * fix: incorrect position of panel for notification(Bug: 284191)
  * fix(dcc-dock-plugin): Interface description text error(Bug: 286501)
  * fix: ActionInvoked returned wrong id (#894)(Bug: 283561)

 -- Deepin Packages Builder <<EMAIL>>  Wed, 20 Nov 2024 14:08:41 +0800

dde-shell (1.99.6) UNRELEASED; urgency=medium

  * bump version to 1.99.6

 -- Wang Zichong <<EMAIL>>  Thu, 14 Nov 2024 10:20:16 +0800

dde-shell (1.99.5) UNRELEASED; urgency=medium

  * bump version to 1.99.5

 -- Zhangkun <<EMAIL>>  Fri, 08 Nov 2024 09:14:16 +0800

dde-shell (1.99.4) unstable; urgency=medium

  * bump version to 1.99.4

 -- Zhangkun <<EMAIL>>  Thu, 07 Nov 2024 16:03:21 +0800

dde-shell (1.99.3) unstable; urgency=medium

  * bump version to 1.99.3

 -- YeShanShan <<EMAIL>>  Thu, 31 Oct 2024 14:51:43 +0800

dde-shell (1.99.2) UNRELEASED; urgency=medium

  * bump version to 1.99.2

 -- YeShanShan <<EMAIL>>  Thu, 31 Oct 2024 10:19:57 +0800

dde-shell (1.99.1) UNRELEASED; urgency=medium

  * bump version to 1.99.1

 -- YeShanShan <<EMAIL>>  Wed, 23 Oct 2024 10:04:25 +0800

dde-shell (1.99.0) UNRELEASED; urgency=medium

  * bump version to 1.99.0

 -- tsic404 <<EMAIL>>  Fri, 11 Oct 2024 16:43:51 +0800

dde-shell (0.0.43) unstable; urgency=medium

  * bump version to 0.0.43

 -- Deepin Packages Builder <<EMAIL>>  Thu, 29 Aug 2024 10:41:01 +0800

dde-shell (0.0.42) unstable; urgency=medium

  * feat: tooltip and preview call to treeland
  * fix: animation when drag from launchpad to dock

 -- tsic404 <<EMAIL>>  Fri, 23 Aug 2024 16:45:08 +0800

dde-shell (0.0.41) unstable; urgency=medium

  * fix: incorrect animation application when dragging(Issue: 10330)
  * fix: Dock following mouse display error when modifying SmartHide(Issue: 10404)
  * fix: plugin process is pulled up again after crashed(Issue: 10400)
  * fix: modify panel popup shadow offset and color(Issue: 10325)
  * fix: forced exit is invalid(Issue: 10436)
  * fix: searchItem right click response range is too large(Issue: 10294)
  * fix: pupup surface doesn't release(Issue: 10121)
  * chore: adjust iconSize for quickpanel(Issue: 9852)

 -- Deepin Packages Builder <<EMAIL>>  Wed, 21 Aug 2024 17:32:09 +0800

dde-shell (0.0.40) unstable; urgency=medium

  * fix: incorrect height for quickpanel's childplugin(Issue: 10036)
  * fix: incorrect backgroundColor for quickpanel's item (#696)(Issue: 10379)

 -- Deepin Packages Builder <<EMAIL>>  Wed, 14 Aug 2024 14:18:35 +0800

dde-shell (0.0.39) unstable; urgency=medium

  * chore: disable hide force-docked item(Issue: 10371)

 -- Deepin Packages Builder <<EMAIL>>  Tue, 13 Aug 2024 20:43:29 +0800

dde-shell (0.0.38) unstable; urgency=medium

  * chore: move network plugin to pinned area(Issue: 10355)
  * feat: hide search, clipboard and workspace when leftAlignment(Issue: 10363)

 -- Deepin Packages Builder <<EMAIL>>  Tue, 13 Aug 2024 16:04:21 +0800

dde-shell (0.0.37) unstable; urgency=medium

  * fix: temporary revert application name change

 -- Deepin Packages Builder <<EMAIL>>  Tue, 13 Aug 2024 10:48:37 +0800

dde-shell (0.0.36) unstable; urgency=medium

  * bump version to 0.0.36

 -- Deepin Packages Builder <<EMAIL>>  Thu, 08 Aug 2024 19:31:54 +0800

dde-shell (0.0.35) unstable; urgency=medium

  * fix: dock covered by fullscreen launchpad(Issue: 10222)
  * fix: drag plugin item cause plugin's event blocked (#663)(Issue: 9819)(Influence: dragging plugin and plugin item display)
  * chore: change the default order of system tray(Issue: 10049)

 -- Deepin Packages Builder <<EMAIL>>  Thu, 08 Aug 2024 15:15:13 +0800

dde-shell (0.0.34) unstable; urgency=medium

  * chore: hide tray plugins by default(Issue: #10049)
  * fix: Modify smart hide to always hide issues(Issue: https://github.com/linuxdeepin/developer-center/issues/10107)
  * fix: Popup can't switch to other by one click(Issue: https://github.com/linuxdeepin/developer-center/issues/10095)
  * fix: mouse position drift(Issue: https://github.com/linuxdeepin/developer-center/issues/9964)
  * fix: dateTime plugin position error
  * fix: popup can't close when click empty area for dock(Issue: https://github.com/linuxdeepin/developer-center/issues/10075)
  * chore: tooltip poupu and panel popup delayed show(Issue: https://github.com/linuxdeepin/developer-center/issues/10119)(Influence: tooltip and panle popup display)
  * fix: dock needs to be on top of the launchpad(Issue: https://github.com/linuxdeepin/developer-center/issues/9996, https://github.com/linuxdeepin/developer-center/issues/9992)
  * fix: PopupWindow position cannot be refreshed(Issue: https://github.com/linuxdeepin/developer-center/issues/10096)
  * fix: Unable to pop up when modifying network pop ups(Issue: https://github.com/linuxdeepin/developer-center/issues/10153)
  * fix: quick panel icon size error(Issue: https://github.com/linuxdeepin/developer-center/issues/9957)
  * fix: adjust border's width for quickpanel(Issue: https://github.com/linuxdeepin/developer-center/issues/9798)
  * fix: adjust ui for stashed(Issue: https://github.com/linuxdeepin/developer-center/issues/9894)
  * fix: Temporarily disable all windows action(Issue: https://github.com/linuxdeepin/developer-center/issues/10172)
  * fix: fullscreen frame follows the dock(Issue: https://github.com/linuxdeepin/developer-center/issues/7188)
  * fix: no tooltip when moving from the volume plugin to the shortcut panel icon (#651)(Influence: shortcut panel tooltip)
  * fix: subMenu can't show
  * fix: The padding and rounded corners of the tooltip are incorrect
  * chore: show bluetooth tray icon by default(Issue: #10049)
  * fix: wrong size(Issue: #10077)
  * fix: dock's frontendWindowRect size is wrong(Issue: https://github.com/linuxdeepin/developer-center/issues/10155)(Influence: frontendWindowRect size)
  * fix: sub menu miss focus(Issue: https://github.com/linuxdeepin/developer-center/issues/10015)
  * fix: adjust radius for stashed

 -- YeShanShan <<EMAIL>>  Tue, 06 Aug 2024 15:37:33 +0800

dde-shell (0.0.33) unstable; urgency=medium

  * fix: Update translation (#611)(issue: 9965)
  * fix: adjust ui for dock's applet(Issue: 8633)
  * feat: support active state for quickpanel's item(Issue: 10020)
  * fix: The dock translation is incorrect(Issue: 9947)
  * fix: popup's height isn't enough(Issue: 9702)
  * fix: adjust ui(Issue: 9995, 9894)
  * fix: Plugin should not be dragged into the application tray(Issue: 10048)
  * feat: Add smart hide function(Issue: 9842)
  * chore: modify the order of systray(Issue: 10049)
  * Revert "fix: unable to set visibility of 'Show Desktop' in DCC"(Issue: 10055)

 -- zyz <<EMAIL>>  Wed, 31 Jul 2024 13:53:47 +0800

dde-shell (0.0.32) unstable; urgency=medium

  * fix: unable to set visibility of 'Show Desktop' in DCC(Issue: 9820)

 -- zyz <<EMAIL>>  Fri, 26 Jul 2024 16:11:19 +0800

dde-shell (0.0.31) unstable; urgency=medium

  * bump version to 0.0.31

 -- zyz <<EMAIL>>  Thu, 25 Jul 2024 22:05:46 +0800

dde-shell (0.0.30) unstable; urgency=medium

  * fix: the item of datetime and showdesktop display overlap(Issue: 9696)
  * fix: add margins to extend mouse area for tray item(Issue: 9720)
  * fix: large radius when exist scaling(Issue: 9818)
  * fix: popup can drag(Issue: 9896)
  * fix: workSpace toolTip text error(Issue: 9877)
  * fix: Tray button rotates as a whole(Issue: 9880)
  * fix: Application tray position drift(Issue: 9603)
  * fix: tray container shows blank area when there is only one item(Issue: 9887)
  * fix: Application preview background color overflow(Issue: 9433)
  * fix: Application preview background color overflow(Issue: 9433)
  * fix: the stash container turns into a small ball(Issue: 9830)
  * refact: using Item instead of Popup for PanelPopup(Issue: 9676)

 -- zyz <<EMAIL>>  Wed, 24 Jul 2024 14:27:44 +0800

dde-shell (0.0.29) unstable; urgency=medium

  * fix: can't hide menu when click outside dock
  * feat: add grabMouse for window

 -- Yingzhen zhao <<EMAIL>>  Fri, 19 Jul 2024 17:50:32 +0800

dde-shell (0.0.28) unstable; urgency=medium

  * fix: dock now show when init
  * feat: support popup to align with dock
  * chore: tray area show dragged item at cursor position
  * chore: using Image to display drag image
  * chore: set the plugin visibility
  * fix: the workspace preview image is displayed abnormally

 -- Wang Fei <<EMAIL>>  Mon, 08 Jul 2024 13:21:32 +0800

dde-shell (0.0.23) unstable; urgency=medium

  * fix: uos-ai disabled(issue: 8593)

 -- tsic404 <<EMAIL>>  Tue, 14 May 2024 20:39:52 +0800

dde-shell (0.0.22) unstable; urgency=medium

  * fix: x11 preview title length too small(Influence: x11 preview title display)

 -- tsic404 <<EMAIL>>  Tue, 14 May 2024 19:01:07 +0800

dde-shell (0.0.21) unstable; urgency=medium

  * fix: app tray icon size not same as plugin

 -- tsic404 <tsic@tsic-v23>  Tue, 14 May 2024 14:44:31 +0800

dde-shell (0.0.20) unstable; urgency=medium

  * fix: brightness-popup's setting button no blur effect and txt color has error
    Thanks to ZhaoYingZhen(Issue: https://github.com/linuxdeepin/developer-center/issues/8470)(Influence: brightness-popup's setting button)
  * fix: tray plugin mouse right click cause dock crash
    Thanks to ZhaoYingZhen(Issue: https://github.com/linuxdeepin/developer-center/issues/8506)(Influence: tray plugin menu display)
  * fix: onboard has same icon with fcitx(issue: #8557)
  * chore: bump version 0.0.20

 -- tsic404 <<EMAIL>>  Tue, 14 May 2024 10:20:00 +0800

dde-shell (0.0.19) unstable; urgency=medium

  * feat: remove uos-ai plugin when uos ai uninstalled

 -- tsic404 <<EMAIL>>  Mon, 13 May 2024 14:09:19 +0800

dde-shell (0.0.18) unstable; urgency=medium

  * feat: add tray dci icons(Issue: https://github.com/linuxdeepin/developer-center/issues/8082)
  * fix: build on qt 6.7
  * fix: build failures with Qt 6.7+
    Thanks to Felix Yan
  * fix: bluetooth popup has no blur effect and wrong position of separator line
    Thanks to ZhaoYingZhen(Issue: https://github.com/linuxdeepin/developer-center/issues/8494)(Influence: bluetooth popup has blur effect)
  * fix: dragging tray icon to dock, but show error app in dock tray area
    Thanks to ZhaoYingZhen(issue: #8480)(Influence: drag tray icon to dock)
  * fix: tray plugin mouse right click menu causing dock to crash
    Thanks to ZhaoYingZhen(Issue: https://github.com/linuxdeepin/developer-center/issues/8506)(Influence: tray plugin mouse right click menu)
  * fix: qt 6.7 cannot build
  * fix: dock not shown with launchpad(issue: #8502)
  * fix: menu can uncheked while double click(issue: #8503)
  * fix: need link xcb-aux and xkbcommon
    Thanks to Hillwood Yang

 -- tsic404 <<EMAIL>>  Mon, 13 May 2024 06:54:37 +0800

dde-shell (0.0.17) unstable; urgency=medium

  * fix: fix the size of app icon when resizing dock size by dcc
    Thanks to ZhaoYingZhen(issue: #8401)(Influence: icon size)
  * fix: event delivery error(issue: #8326)
  * fix: button's icon is not clear(Issue: https://github.com/linuxdeepin/developer-center/issues/8409)
  * fix: drag mark error(issue: #8208)
  * fix: refresh when bluetooth panel shown(issue: #8279)
  * fix: some icon too large(issue: #8372)
  * fix: tips position error(issue: #8197)
  * chore: remove warning
  * fix: dock wake area position not right sometime(issue: #8163)
  * fix: popup window always hide
  * fix: power popup cannot show
  * fix: menu checked status not right(issue: #8478)
  * fix: click dragArea can open app(issue: #8479)

 -- tsic404 <<EMAIL>>  Fri, 10 May 2024 11:09:47 +0800

dde-shell (0.0.16) unstable; urgency=medium

  * fix: font warning when exit
  * chore: using Panel to find children applet
  * fix: modify launch system-monitor by AM dbus (#312)
    Thanks to ZhaoYingZhen(issue: 8319)(Influence: dock can display system-monitor app icon)
  * fix: fix incorrect bluetooth activation status icon in the tray quick panel (#279)
    Thanks to ZhaoYingZhen(issue: 8237)(Influence: bluetooth activation status icon display)
  * fix: opacity can't flow changed with dde-appearance(Issue: https://github.com/linuxdeepin/developer-center/issues/8250)
  * fix: dbus warning when exit
  * fix: background change when hover(Issue: https://github.com/linuxdeepin/developer-center/issues/8275)
  * fix: fix show-desktop line color error (#309)
    Thanks to ZhaoYingZhen(issue: 8284)(Influence: show-desktop line)
  * fix: fix the power icon displays incorrectly (#320)
    Thanks to ZhaoYingZhen(issue: 8347)(Influence: power icon display)
  * chore: set QT_QUICK_FLICKABLE_WHEEL_DECELERATION env
  * fix: fix the problem of abnormal location of uos-ai plugin(issue: 8322)(Influence: uos-ai plugin display)
  * feat: destroy plugins manually
  * fix: dock not in left when no left plugins(issue: 8353, 8352)

 -- Yixue Wang <<EMAIL>>  Tue, 07 May 2024 15:42:20 +0800

dde-shell (0.0.15) unstable; urgency=medium

  * fix: plugin icons should not changed in dcc(issue: 8245)
  * fix: Solve the problem of small plugin icon in the quick panel popup under high zoom (#299)
    Thanks to ZhaoYingZhen(issue: 8111)(Influence: quick panel plugin icons size)
  * feat: Move tooltipShowTimer to Item
    Thanks to zhangkun
  * fix: bluetooth and network plugin applet layout error when clicked by icon on quick panel(issue: 7704)
  * chore: update icon such as weixin(wine)(issue: 8280)
  * fix: tooltip shown when drag dock(issue: 8287)
  * chore: make multitaskview can set invisible by dcc(issue: 8312)
  * fix: window display abnormality for PanelToolTip(Issue: https://github.com/linuxdeepin/developer-center/issues/8071)
  * fix: dock crash when get currenActiveWindow is Null
  * fix: fix preview widget display abnormally(issue: 8311)(Influence: preview display)
  * fix: When dragging the tray application, tray expansion box is hidden.(issue: 8315)

 -- Deepin Packages Builder <<EMAIL>>  Mon, 29 Apr 2024 15:43:53 +0800

dde-shell (0.0.14) unstable; urgency=medium

  * fix: crashed when open dock's menu(Issue: https://github.com/linuxdeepin/developer-center/issues/8243)
  * fix: double release pointer
  * fix: invalid fallback for DesktopfileAbstractParser
  * fix: crashed when load plugins in sometimes
  * fix: crashed when QScrollArea during deconstruction
  * fix: "showtimetofull" option didn't work(Issue: https://github.com/linuxdeepin/developer-center/issues/8144)
  * fix: application preview stuttering.
    Thanks to zhangkun(Issue: https://github.com/linuxdeepin/developer-center/issues/8095)
  * chore: missing lincense
  * fix: incorrect resources for device icon(Issue: https://github.com/linuxdeepin/developer-center/issues/8238)
  * chore: modify ts file (#281)
    Thanks to ZhaoYingZhen(issue: 8270)(Influence: translation display)
  * fix: crashed by qmlcache(Issue: https://github.com/linuxdeepin/developer-center/issues/8289)
  * fix: quick panel popup has focus (#293)
    Thanks to ZhaoYingZhen(Influence: quick panel popup has no focus)
  * chore: add uos-ai tray plugin (#283)
    Thanks to ZhaoYingZhen(issue: 8247)(Influence: uos-ai tray plugin icon display)
  * fix: modify plugin hover style
  * fix: workspace not localize(issue: 8223, 8278)
  * feat: Hide multitask button when composition is disabled(Issue: https://github.com/linuxdeepin/developer-center/issues/8224)
  * fix: crashed by tipWidget when exit
  * fix: fix `mapToGlobal` error
  * fix: notification plugin not generated in correct location in debug mode(issue: 8245)
  * fix: tips position error(issue: 8197)
  * fix: some widget layout margin incorrect(issue: 7704)
  * [dde-shell] Updates for project Deepin Desktop Environment (#277)
    Thanks to transifex-integration[bot]

 -- Deepin Packages Builder <<EMAIL>>  Sun, 28 Apr 2024 13:14:13 +0800

dde-shell (0.0.13) unstable; urgency=medium

  * chore: adjust radius for ToolTips
  * fix: unable to connect to wifi with password for the first time(Issue: https://github.com/linuxdeepin/developer-center/issues/7966)
  * fix: multitaskview not scale realtime
    Thanks to justforlxz
  * fix: When the dock is hidden, the system tray expansion box is not hidden.(issue: 8153)
  * fix: set cursor shape for dock and tray(Issue: https://github.com/linuxdeepin/developer-center/issues/8181)
  * fix: transparent windows cannot be blurred(Issue: https://github.com/linuxdeepin/developer-center/issues/8211)
  * feat: adjust ui
  * fix: deepin-launcher icon incorrect on bloom theme(Issue: https://github.com/linuxdeepin/developer-center/issues/8206)
  * fix: The dock cannot record the tray icon dragged to the dock plug-in area.(issue: 8189)(Influence: tray icons display)
  * fix: Clicking the collapse button does not collapse the tray view.(issue: 8175)
  * fix: multitaskview ts
    Thanks to justforlxz(Issue: https://github.com/linuxdeepin/developer-center/issues/8225)

 -- Deepin Packages Builder <<EMAIL>>  Thu, 25 Apr 2024 21:28:43 +0800

dde-shell (0.0.12) unstable; urgency=medium

  * fix: popup window cannot show on first run
  * chore: add open workspace item
  * fix: After logging out, the workspace icons are not displayed
  * fix: some icon not clear
  * refact: update ColorSelector family for dock panel
  * fix: solve the problem of blurry icons in the tray area
  * fix: preview title not elided
  * fix: icon blurry
  * fix: text color of ToolTip doesn't change with theme
  * fix: solve the problem of the dock jumping when the height changes

 -- tsic404 <<EMAIL>>  Wed, 24 Apr 2024 15:30:04 +0800

dde-shell (0.0.11) unstable; urgency=medium

  * fix: don't emit FrontendWindowRectChanged signal

 -- Wang Fei <<EMAIL>>  Tue, 23 Apr 2024 14:24:08 +0800

dde-shell (0.0.10) unstable; urgency=medium

  * fix: x11 preview show without content
  * fix: appitem not remove when uninstalled
  * fix: fix popup position error
  * fix: fix some icon not clear when system scaled to 1.25X
  * fix: dde-control-center unable to set dockSize
  * fix: modify the distance between app icons and indicator margins
  * fix: workspace item not preserving aspect ratio
  * fix: launchpad's left align
  * fix: battery plugin tooltips show remaining time info
  * feat: add tooltip for dock
  * chore: update icon
  * fix: don't save the visibility
  * fix: workspace can interactive
  * fix: dock centerPart not screen center
  * fix: no icon when desktopfile no icon defined
  * fix: not set themeType to DWindow
  * fix: dock height change causes the app icon to be blurred
  * fix: Task Manager preview size is not correct
  * fix: null rootObject of launchpad
  * chore: update tray tips radius
  * fix: datetime plugin not update
  * fix: error palette when get pixmap form dci
  * fix: adjust indicator palette
  * fix: not show search menu
  * fix: GrandSearch without translate
  * fix: Alignment without translate
  * fix: app icon not centered

 -- tsic404 <<EMAIL>>  Fri, 19 Apr 2024 15:09:33 +0800

dde-shell (0.0.9) unstable; urgency=medium

  * fix: not set window icon gemeotry for window
  * fix: menu invisible
  * fix: rename mode to Alignment
  * fix: player icon not update
  * feat: adjust search and clipboard ui
  * fix: the distance between the preview window and the dock is not always 10 pixels
  * fix: the close button of the preview widget has no hover state
  * fix: unable to launch terminal with quake-mode by dock menu
  * chore: tiny code format
  * chore: avoid hardcode path
  * fix: fix no distance between dateTime and showDesktop
  * fix: nullptr for dde-appearance
  * fix: can't show system monitor plugin popup widget
  * fix: calendar icon error on dock when click datetime plugin
  * fix: fix translations error
  * fix: plugin border color not clear when system scaled to 1.25X
  * fix: fix the problem of drag tray icon into dock invalid
  * fix: fix quick tray icon not show in vertical center

 -- tsic404 <<EMAIL>>  Thu, 18 Apr 2024 13:30:19 +0800

dde-shell (0.0.8) unstable; urgency=medium

  * fix: dock item get default icon when set auto start
  * feat: limit opacity from dde-appearance
  * fix: dock panel never accept focus
  * chore: change to more common method to insert old dock widget
  * fix: appitem windows order always changed
  * fix: x11 preview not fit screen
  * fix: icon blink when resize
  * fix: dock get worng positoin when screen size changed
  * fix: icon missing after drag
  * feat: add disk-mount plugin
  * feat: add system-monitor plugin
  * feat: add dock-network-plugin
  * feat: add translation of dde-shell plugin

 -- tsic404 <<EMAIL>>  Thu, 11 Apr 2024 21:05:29 +0800

dde-shell (0.0.7) unstable; urgency=medium

  * fix: dock menu not shown
  * fix: dock not show while HideMode changed in hiden
  * fix: dock centerAlignment not work on left/right
  * fix: x11 preview not auto hide
  * fix: undocked item still existed when restart dock
  * fix: show window even is bypassWM
  * fix: show deepin application with deepin prefix
  * fix: dock tray tips position error
  * fix: brightness widget display is incomplete
  * fix: Show desktop item is not displayed in the bottom right corner
  * fix: repair the time display is not clear
  * feat: we can hide dock plugin in dde-control-center

 -- tsic404 <<EMAIL>>  Mon, 08 Apr 2024 18:56:30 +0800

dde-shell (0.0.6) unstable; urgency=medium

  * new dock ui
  * dock x11 preview
  * dock tmp tray
  * add clipboard/workspace/search applet for dock
  * feat: support to get other root applet
  * refact: launching app by dde-am tool
  * feat: add AM applet to reset appsLaunchedTimes
  * fix: can't filter root applet when loaded failed
  * chore: disable notification and osd in release

 -- tsic404 <<EMAIL>>  Tue, 26 Mar 2024 17:46:41 +0800

dde-shell (0.0.5) unstable; urgency=medium

  * feat: add application preview in dock
  * fix: worng place and way to install libdde-dockplugin-interface
  * fix: missing ds.dock configs
  * fix: tooltip reparent error
  * fix: show-desktop shown on treeland
  * feat: set radius for dock in Fashion mode
  * fix: plugin theme not follow dock
  * fix: tmp adjust dock ui and datetime theme
  * chore: dock disable some menu on release

 -- tsic404 <<EMAIL>>  Wed, 10 Jan 2024 17:25:52 +0800

dde-shell (0.0.4) unstable; urgency=medium

  * chore: optimize cmake
  * chore: promote qt wayland version constraint
  * fix: menu not closed when popup second one
  * feat: add PanelPopup to manage Panel's popup
  * refact: async load qml
  * fix: PanelPopup's size is wrong
  * fix: protocol error when changing anchors
  * feat: add PanelToolTip
  * feat: modify data model in dnd
  * feat: add dock quick plugins
  * chore: mark getters const
  * fix: drag handler still active when finished
  * refact: modify containment's appletItems type
  * refact: adjust dock project
  * fix: SortFilterModel can't update when item's property changed
  * refact: use Binding instead of Qt.binding
  * feat: add sub Applet's property example

 -- YeShanShan <<EMAIL>>  Thu, 28 Dec 2023 17:28:59 +0800

dde-shell (0.0.3) unstable; urgency=medium

  * fix: transparent mouse event
  * fix: unable set margins when anchor three sides in xorg
  * fix: missing files for dock plugin

 -- YeShanShan <<EMAIL>>  Tue, 19 Dec 2023 15:20:17 +0800

dde-shell (0.0.2) unstable; urgency=medium

  * feat: add dock(issue: 6221)
  * feat: support multi instance with same Applet(Issue: https://github.com/linuxdeepin/developer-center/issues/6245)
  * chore: remove warning
  * feat: add notification to display bubble(Issue: https://github.com/linuxdeepin/developer-center/issues/6095)
  * optimize: use triple equal operator
  * chore: correct typos in pluginfactory.cpp
    Thanks to Felix Yan
  * feat: support to find parent applet
  * chore: Add Processed type
  * feat: support to use labs-platform Controls
  * chore: Change PUBLIC to PRIVATE for PkgConfig::XCB
  * fix: exclusionZone type error
  * feat: support systemd to manage plugin
  * fix: can't access Applet for non Item
  * fix: release rootObject when delete Applet
  * feat: support to access the nearest Panel
  * feat: add treeland-foreign-toplevel-manager support
  * chore: modify qml import path
  * chore: correct typo
  * fix: lacking implementation of get_popup
  * chore: optimize log
  * feat: add qml plugin dockpanel
  * feat: add window and state indicator
  * feat: add drag and drop function
  * fix: missing systemd package
  * fix: missing labs package
  * feat: Add new UI about notification
 
 -- YeShanShan <<EMAIL>>  Fri, 15 Dec 2023 09:20:22 +0800

dde-shell (0.0.1) unstable; urgency=medium

  * feat: add plugin framework structure and example(Issue: https://github.com/linuxdeepin/developer-center/issues/5810)
  * feat: add layershell wrap(Issue: https://github.com/linuxdeepin/developer-center/issues/5808)
  * feat: load all root applet by default(Issue: https://github.com/linuxdeepin/developer-center/issues/5965)
  * fix: failed fetch Applet attached property for Panel
  * fix: Fix typo in cmakelist
  * fix: target link error in qt6.6
  * chore: redefine DS_SHARE
  * feat: don't load disabled applet by option
  * fix: missing dependencies for debian
  * feat: add x11 layershell emulation
  * feat: add osd plugin(Issue: https://github.com/linuxdeepin/developer-center/issues/6026)
  * fix: missing some dependices for debian(Issue: https://github.com/linuxdeepin/developer-center/issues/6026)
  * chore: rename 'corona' to 'panel'
  * feat: support to access AppletItem for Applet
  * feat: add plugin's check
  * fix: example crashed
  * feat: filter unload applet
  * fix: layershell wrap bugs
  * feat: add signal handler to release applets

 -- YeShanShan <<EMAIL>>  Fri, 17 Nov 2023 08:58:18 +0800

dde-shell (0.0.0) unstable; urgency=medium

  * chore: init

 -- YeShanShan <<EMAIL>>  Thu, 19 Oct 2023 15:49:28 +0800
