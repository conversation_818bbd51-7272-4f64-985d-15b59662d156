Source: dde-shell
Section: DDE
Priority: optional
Maintainer: Deepin Packages Builder <<EMAIL>>
Build-Depends:
 debhelper-compat ( =12),
 cmake,
 systemd,
 qt6-base-dev,
 qt6-declarative-dev,
 qt6-base-dev-tools,
 qt6-tools-dev,
 qt6-wayland ( >=6.8.0-0deepin6),
 qt6-wayland-dev ( >=6.8.0-0deepin6),
 qt6-wayland-dev-tools,
 qt6-wayland-private-dev,
 wayland-protocols,
 treeland-protocols,
 libdtk6gui-dev,
 libdtk6core-bin,
 libdtk6core-dev,
 libdtk6widget-dev,
 libdtkcommon-dev,
 libicu-dev,
 libxcb-ewmh-dev,
 libxcb-res0-dev,
 libxcb-util-dev,
 libxcb-icccm4-dev,
 libxcb1-dev,
 libxtst-dev,
 libgtest-dev,
 extra-cmake-modules,
 libqt6svg6,
 libdtk6declarative, qml6-module-qtquick-controls2-styles-chameleon, qt6-declarative-private-dev,
 libyaml-cpp-dev,
 qt6-l10n-tools, qt6-svg-dev, dde-tray-loader-dev (> 1.99.14),
 dde-application-manager-api (> 1.2.23)
Standards-Version: 3.9.8
Homepage: https://github.com/linuxdeepin/dde-shell

Package: libdde-shell
Architecture: any
Depends: ${shlibs:Depends}, ${misc:Depends}
Multi-Arch: same
Description: DDE Shell library
 DDE Shell is a plugin system that integrates plugins developed based on this plugin system into DDE.

Package: libdde-shell-dev
Architecture: any
Depends: ${misc:Depends}, libdde-shell( =${binary:Version}),
  qt6-base-dev, libdtk6core-dev, qt6-tools-dev, qt6-declarative-dev
Description: DDE Shell devel library
 DDE Shell is a plugin system that integrates plugins developed based on this plugin system into DDE.

Package: dde-shell
Architecture: any
Depends: ${shlibs:Depends}, ${misc:Depends}, libdde-shell( =${binary:Version}),
  libqt6svg6, qml6-module-qt-labs-platform,
  libqt6sql6-sqlite,
  qt6-wayland ( >=6.8),
  qml6-module-qtquick-layouts, qml6-module-qtquick-window,
  libdtk6declarative, qml6-module-qtquick-controls2-styles-chameleon,
  qml6-module-qtquick-layouts, qml6-module-qtquick-window,
  qml6-module-qt-labs-platform, qml6-module-qt-labs-qmlmodels,
  dde-tray-loader (> 1.99.8)
Multi-Arch: same
Description: An wrapper for developed based on dde-shell plugin system

Package: dde-shell-example
Architecture: any
Depends: ${shlibs:Depends}, ${misc:Depends}, libdde-shell( =${binary:Version}),
  qml6-module-qt-labs-platform
Multi-Arch: same
Description: DDE Shell example
 This package contains some plugins based on dde-shell plugin system.
