# SPDX-FileCopyrightText: 2023 UnionTech Software Technology Co., Ltd.
#
# SPDX-License-Identifier: CC0-1.0

.clang-format
build*/
.vscode/settings.json
*.user
.cache/
panels/dock/tray/frame/dbusinterface/generation_dbus_interface/
panels/dock/tray/plugins/sound/dbusinterface/generation_dbus_interface/
panels/dock/tray/plugins/bluetooth/dbusinterface/generation_dbus_interface/
panels/dock/tray/plugins/airplane-mode/dbusinterface/generation_dbus_interface/
panels/dock/tray/plugins/keyboard-layout/dbusinterface/generation_dbus_interface/
panels/dock/tray/plugins/power/dbusinterface/generation_dbus_interface/
obj-x86_64-linux-gnu/
*.autosave
*/utils/dbus/xml2cpp/*_interface.*
