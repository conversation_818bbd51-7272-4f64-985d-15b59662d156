# This file is managed by Qt C<PERSON>, do not edit!

set("CMAKE_PROJECT_INCLUDE_BEFORE" "/home/<USER>/Desktop/myrepo/dde-shell/.qtc/package-manager/auto-setup.cmake" CACHE "FILEPATH" "" FORCE)
set("CMAKE_PREFIX_PATH" "/usr" CACHE "PATH" "" FORCE)
set("CMAKE_BUILD_TYPE" "Build" CACHE "STRING" "" FORCE)
set("CMAKE_C_COMPILER" "/bin/gcc" CACHE "FILEPATH" "" FORCE)
set("CMAKE_GENERATOR" "Unix Makefiles" CACHE "STRING" "" FORCE)
set("CMAKE_COLOR_DIAGNOSTICS" "ON" CACHE "BOOL" "" FORCE)
set("CMAKE_CXX_COMPILER" "/bin/g++" CACHE "FILEPATH" "" FORCE)
set("CMAKE_CXX_FLAGS_INIT" "" CACHE "STRING" "" FORCE)
set("QT_QMAKE_EXECUTABLE" "/usr/bin/qmake6" CACHE "FILEPATH" "" FORCE)
set("CMAKE_BUILD_TYPE" "Build" CACHE "STRING" "" FORCE)