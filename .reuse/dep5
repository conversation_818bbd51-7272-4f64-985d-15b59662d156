Format: https://www.debian.org/doc/packaging-manuals/copyright-format/1.0/
Upstream-Name: dde-launchpad
Upstream-Contact: UnionTech Software Technology Co., Ltd.  <>
Source: https://github.com/linuxdeepin/dde-launchpad

# packaging manifests
Files: debian/* rpm/* archlinux/* .packit.yaml
Copyright: None
License: CC0-1.0

# cmake
Files: misc/*.in cmake/*.cmake
Copyright: None
License: CC0-1.0

# configuration files for CI scripts/tools
Files: .github/* .obs/* .tx/transifex.yaml
Copyright: None
License: CC0-1.0

# README and documentations
Files: README.md README.*.md docs/*.md
Copyright: UnionTech Software Technology Co., Ltd.
License: CC-BY-4.0

# images
Files: */icons/*.dci */icons/*.svg */texts/*.svg
Copyright: UnionTech Software Technology Co., Ltd.
License: GPL-3.0-or-later

# resource sheet
Files: *.qrc */qmldir
Copyright: None
License: CC0-1.0

# translations
Files: */translations/*.ts
Copyright: UnionTech Software Technology Co., Ltd.
License: GPL-3.0-or-later

# dbus api xml files
Files: *.deepin.dde.*.xml */org.desktopspec.*.xml
Copyright: UnionTech Software Technology Co., Ltd.
License: GPL-3.0-or-later

Files: panels/dock/api/dbus/**.xml panels/dock/taskmanager/api/dbus/**.xml panels/dock/dockplugin/protocol/*.xml
Copyright: UnionTech Software Technology Co., Ltd.
License: GPL-3.0-or-later

# package json
Files: *.json
Copyright: None
License: CC0-1.0

# wayland protocols
Files: frame/layershell/protocol/wlr-layer-shell-unstable-v1.xml
Copyright: 2017 Drew DeVault
License: MIT

Files: panels/dock/taskmanager/api/wayland-protocols/treeland-foreign-toplevel-manager-v1.xml
Copyright: None
License: CC0-1.0

# test bash
Files: *.sh
Copyright: None
License: CC0-1.0

# services
Files: misc/*.service
Copyright: None
License: CC0-1.0

# tray
Files: panels/dock/tray/*
Copyright: UnionTech Software Technology Co., Ltd.
License: GPL-3.0-or-later

Files: toolGenerate/**/*
Copyright: None
License: CC0-1.0


