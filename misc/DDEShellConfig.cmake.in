@PACKAGE_INIT@
include(CMakeFindDependencyMacro)
find_dependency(Dtk@DTK_VERSION_MAJOR@Core)
find_dependency(Dtk@DTK_VERSION_MAJOR@Gui)
find_package(Qt@QT_VERSION_MAJOR@ COMPONENTS Qml Quick REQUIRED)

include(${CMAKE_CURRENT_LIST_DIR}/DDEShellTargets.cmake)
set(DDE_SHELL_PACKAGE_INSTALL_DIR @CMAKE_INSTALL_PREFIX@/@DDE_SHELL_PACKAGE_INSTALL_DIR@)
set(DDE_SHELL_PLUGIN_INSTALL_DIR @CMAKE_INSTALL_PREFIX@/@DDE_SHELL_PLUGIN_INSTALL_DIR@)
set(DDE_SHELL_TRANSLATION_INSTALL_DIR @CMAKE_INSTALL_PREFIX@/@DDE_SHELL_TRANSLATION_INSTALL_DIR@)
check_required_components(Dtk@DTK_VERSION_MAJOR@Core)

include("${CMAKE_CURRENT_LIST_DIR}/DDEShellPackageMacros.cmake")
