/*
 * This file was generated by qdbusxml2cpp version 0.8
 * Command line was: qdbusxml2cpp ./dde-shell/panels/dock/taskmanager/api/dbus/org.deepin.ds.dock.taskmanager.xml -a ./dde-shell/toolGenerate/qdbusxml2cpp/org.deepin.ds.dock.taskmanagerAdaptor -i ./dde-shell/toolGenerate/qdbusxml2cpp/org.deepin.ds.dock.taskmanager.h
 *
 * qdbusxml2cpp is Copyright (C) 2017 The Qt Company Ltd.
 *
 * This is an auto-generated file.
 * This file may have been hand-edited. Look for HAND-EDIT comments
 * before re-generating it.
 */

#ifndef ORG_DEEPIN_DS_DOCK_TASKMANAGERADAPTOR_H
#define ORG_DEEPIN_DS_DOCK_TASKMANAGERADAPTOR_H

#include <QtCore/QObject>
#include <QtDBus/QtDBus>
#include "./dde-shell/toolGenerate/qdbusxml2cpp/org.deepin.ds.dock.taskmanager.h"
QT_BEGIN_NAMESPACE
class QByteArray;
template<class T> class QList;
template<class Key, class Value> class QMap;
class QString;
class QStringList;
class QVariant;
QT_END_NAMESPACE

/*
 * Adaptor class for interface org.deepin.ds.Dock.TaskManager
 */
class TaskManagerAdaptor: public QDBusAbstractAdaptor
{
    Q_OBJECT
    Q_CLASSINFO("D-Bus Interface", "org.deepin.ds.Dock.TaskManager")
    Q_CLASSINFO("D-Bus Introspection", ""
"  <interface name=\"org.deepin.ds.Dock.TaskManager\">\n"
"    <property access=\"readwrite\" type=\"b\" name=\"windowSplit\"/>\n"
"    <property access=\"readwrite\" type=\"b\" name=\"allowForceQuit\"/>\n"
"  </interface>\n"
        "")
public:
    TaskManagerAdaptor(QObject *parent);
    virtual ~TaskManagerAdaptor();

public: // PROPERTIES
    Q_PROPERTY(bool allowForceQuit READ allowForceQuit WRITE setAllowForceQuit)
    bool allowForceQuit() const;
    void setAllowForceQuit(bool value);

    Q_PROPERTY(bool windowSplit READ windowSplit WRITE setWindowSplit)
    bool windowSplit() const;
    void setWindowSplit(bool value);

public Q_SLOTS: // METHODS
Q_SIGNALS: // SIGNALS
};

#endif
