/*
 * This file was generated by qdbusxml2cpp version 0.8
 * Command line was: qdbusxml2cpp ./dde-shell/panels/notification/server/dbus/xml/org.deepin.dde.SessionManager1.xml -a ./dde-shell/toolGenerate/qdbusxml2cpp/org.deepin.dde.SessionManager1Adaptor -i ./dde-shell/toolGenerate/qdbusxml2cpp/org.deepin.dde.SessionManager1.h
 *
 * qdbusxml2cpp is Copyright (C) 2017 The Qt Company Ltd.
 *
 * This is an auto-generated file.
 * Do not edit! All changes made to it will be lost.
 */

#include "./dde-shell/toolGenerate/qdbusxml2cpp/org.deepin.dde.SessionManager1Adaptor.h"
#include <QtCore/QMetaObject>
#include <QtCore/QByteArray>
#include <QtCore/QList>
#include <QtCore/QMap>
#include <QtCore/QString>
#include <QtCore/QStringList>
#include <QtCore/QVariant>

/*
 * Implementation of adaptor class SessionManager1Adaptor
 */

SessionManager1Adaptor::SessionManager1Adaptor(QObject *parent)
    : QDBusAbstractAdaptor(parent)
{
    // constructor
    setAutoRelaySignals(true);
}

SessionManager1Adaptor::~SessionManager1Adaptor()
{
    // destructor
}

QString SessionManager1Adaptor::currentUid() const
{
    // get the value of property CurrentUid
    return qvariant_cast< QString >(parent()->property("CurrentUid"));
}

bool SessionManager1Adaptor::locked() const
{
    // get the value of property Locked
    return qvariant_cast< bool >(parent()->property("Locked"));
}

int SessionManager1Adaptor::stage() const
{
    // get the value of property Stage
    return qvariant_cast< int >(parent()->property("Stage"));
}

bool SessionManager1Adaptor::AllowSessionDaemonRun()
{
    // handle method call org.deepin.dde.SessionManager1.AllowSessionDaemonRun
    bool out0;
    QMetaObject::invokeMethod(parent(), "AllowSessionDaemonRun", Q_RETURN_ARG(bool, out0));
    return out0;
}

bool SessionManager1Adaptor::CanHibernate()
{
    // handle method call org.deepin.dde.SessionManager1.CanHibernate
    bool out0;
    QMetaObject::invokeMethod(parent(), "CanHibernate", Q_RETURN_ARG(bool, out0));
    return out0;
}

bool SessionManager1Adaptor::CanLogout()
{
    // handle method call org.deepin.dde.SessionManager1.CanLogout
    bool out0;
    QMetaObject::invokeMethod(parent(), "CanLogout", Q_RETURN_ARG(bool, out0));
    return out0;
}

bool SessionManager1Adaptor::CanReboot()
{
    // handle method call org.deepin.dde.SessionManager1.CanReboot
    bool out0;
    QMetaObject::invokeMethod(parent(), "CanReboot", Q_RETURN_ARG(bool, out0));
    return out0;
}

bool SessionManager1Adaptor::CanShutdown()
{
    // handle method call org.deepin.dde.SessionManager1.CanShutdown
    bool out0;
    QMetaObject::invokeMethod(parent(), "CanShutdown", Q_RETURN_ARG(bool, out0));
    return out0;
}

bool SessionManager1Adaptor::CanSuspend()
{
    // handle method call org.deepin.dde.SessionManager1.CanSuspend
    bool out0;
    QMetaObject::invokeMethod(parent(), "CanSuspend", Q_RETURN_ARG(bool, out0));
    return out0;
}

void SessionManager1Adaptor::ForceLogout()
{
    // handle method call org.deepin.dde.SessionManager1.ForceLogout
    QMetaObject::invokeMethod(parent(), "ForceLogout");
}

void SessionManager1Adaptor::ForceReboot()
{
    // handle method call org.deepin.dde.SessionManager1.ForceReboot
    QMetaObject::invokeMethod(parent(), "ForceReboot");
}

void SessionManager1Adaptor::ForceShutdown()
{
    // handle method call org.deepin.dde.SessionManager1.ForceShutdown
    QMetaObject::invokeMethod(parent(), "ForceShutdown");
}

void SessionManager1Adaptor::Logout()
{
    // handle method call org.deepin.dde.SessionManager1.Logout
    QMetaObject::invokeMethod(parent(), "Logout");
}

void SessionManager1Adaptor::PowerOffChoose()
{
    // handle method call org.deepin.dde.SessionManager1.PowerOffChoose
    QMetaObject::invokeMethod(parent(), "PowerOffChoose");
}

void SessionManager1Adaptor::Reboot()
{
    // handle method call org.deepin.dde.SessionManager1.Reboot
    QMetaObject::invokeMethod(parent(), "Reboot");
}

bool SessionManager1Adaptor::Register(const QString &in0)
{
    // handle method call org.deepin.dde.SessionManager1.Register
    bool out0;
    QMetaObject::invokeMethod(parent(), "Register", Q_RETURN_ARG(bool, out0), Q_ARG(QString, in0));
    return out0;
}

void SessionManager1Adaptor::RequestHibernate()
{
    // handle method call org.deepin.dde.SessionManager1.RequestHibernate
    QMetaObject::invokeMethod(parent(), "RequestHibernate");
}

void SessionManager1Adaptor::RequestLock()
{
    // handle method call org.deepin.dde.SessionManager1.RequestLock
    QMetaObject::invokeMethod(parent(), "RequestLock");
}

void SessionManager1Adaptor::RequestLogout()
{
    // handle method call org.deepin.dde.SessionManager1.RequestLogout
    QMetaObject::invokeMethod(parent(), "RequestLogout");
}

void SessionManager1Adaptor::RequestReboot()
{
    // handle method call org.deepin.dde.SessionManager1.RequestReboot
    QMetaObject::invokeMethod(parent(), "RequestReboot");
}

void SessionManager1Adaptor::RequestShutdown()
{
    // handle method call org.deepin.dde.SessionManager1.RequestShutdown
    QMetaObject::invokeMethod(parent(), "RequestShutdown");
}

void SessionManager1Adaptor::RequestSuspend()
{
    // handle method call org.deepin.dde.SessionManager1.RequestSuspend
    QMetaObject::invokeMethod(parent(), "RequestSuspend");
}

void SessionManager1Adaptor::SetLocked(bool in0)
{
    // handle method call org.deepin.dde.SessionManager1.SetLocked
    QMetaObject::invokeMethod(parent(), "SetLocked", Q_ARG(bool, in0));
}

void SessionManager1Adaptor::Shutdown()
{
    // handle method call org.deepin.dde.SessionManager1.Shutdown
    QMetaObject::invokeMethod(parent(), "Shutdown");
}

void SessionManager1Adaptor::ToggleDebug()
{
    // handle method call org.deepin.dde.SessionManager1.ToggleDebug
    QMetaObject::invokeMethod(parent(), "ToggleDebug");
}

