/*
 * This file was generated by qdbusxml2cpp version 0.8
 * Command line was: qdbusxml2cpp ./dde-shell/panels/dock/taskmanager/api/dbus/org.deepin.ds.dock.taskmanager.item.xml -a ./dde-shell/toolGenerate/qdbusxml2cpp/org.deepin.ds.dock.taskmanager.itemAdaptor -i ./dde-shell/toolGenerate/qdbusxml2cpp/org.deepin.ds.dock.taskmanager.item.h
 *
 * qdbusxml2cpp is Copyright (C) 2017 The Qt Company Ltd.
 *
 * This is an auto-generated file.
 * This file may have been hand-edited. Look for HAND-EDIT comments
 * before re-generating it.
 */

#ifndef ORG_DEEPIN_DS_DOCK_TASKMANAGER_ITEMADAPTOR_H
#define ORG_DEEPIN_DS_DOCK_TASKMANAGER_ITEMADAPTOR_H

#include <QtCore/QObject>
#include <QtDBus/QtDBus>
#include "./dde-shell/toolGenerate/qdbusxml2cpp/org.deepin.ds.dock.taskmanager.item.h"
QT_BEGIN_NAMESPACE
class QByteArray;
template<class T> class QList;
template<class Key, class Value> class QMap;
class QString;
class QStringList;
class QVariant;
QT_END_NAMESPACE

/*
 * Adaptor class for interface org.deepin.ds.Dock.TaskManager.Item
 */
class ItemAdaptor: public QDBusAbstractAdaptor
{
    Q_OBJECT
    Q_CLASSINFO("D-Bus Interface", "org.deepin.ds.Dock.TaskManager.Item")
    Q_CLASSINFO("D-Bus Introspection", ""
"  <interface name=\"org.deepin.ds.Dock.TaskManager.Item\">\n"
"    <property access=\"readwrite\" type=\"b\" name=\"isActive\"/>\n"
"    <property access=\"readwrite\" type=\"b\" name=\"isDocked\"/>\n"
"    <property access=\"read\" type=\"s\" name=\"id\"/>\n"
"    <property access=\"read\" type=\"s\" name=\"name\"/>\n"
"    <property access=\"read\" type=\"s\" name=\"icon\"/>\n"
"    <property access=\"read\" type=\"s\" name=\"menus\"/>\n"
"    <method name=\"setDocked\">\n"
"      <arg direction=\"in\" type=\"b\" name=\"docked\"/>\n"
"    </method>\n"
"    <method name=\"handleClick\">\n"
"      <arg direction=\"in\" type=\"s\" name=\"action\"/>\n"
"    </method>\n"
"  </interface>\n"
        "")
public:
    ItemAdaptor(QObject *parent);
    virtual ~ItemAdaptor();

public: // PROPERTIES
    Q_PROPERTY(QString icon READ icon)
    QString icon() const;

    Q_PROPERTY(QString id READ id)
    QString id() const;

    Q_PROPERTY(bool isActive READ isActive WRITE setIsActive)
    bool isActive() const;
    void setIsActive(bool value);

    Q_PROPERTY(bool isDocked READ isDocked WRITE setIsDocked)
    bool isDocked() const;
    void setIsDocked(bool value);

    Q_PROPERTY(QString menus READ menus)
    QString menus() const;

    Q_PROPERTY(QString name READ name)
    QString name() const;

public Q_SLOTS: // METHODS
    void handleClick(const QString &action);
    void setDocked(bool docked);
Q_SIGNALS: // SIGNALS
};

#endif
