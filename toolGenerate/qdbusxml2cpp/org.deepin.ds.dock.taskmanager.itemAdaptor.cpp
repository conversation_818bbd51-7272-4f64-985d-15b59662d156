/*
 * This file was generated by qdbusxml2cpp version 0.8
 * Command line was: qdbusxml2cpp ./dde-shell/panels/dock/taskmanager/api/dbus/org.deepin.ds.dock.taskmanager.item.xml -a ./dde-shell/toolGenerate/qdbusxml2cpp/org.deepin.ds.dock.taskmanager.itemAdaptor -i ./dde-shell/toolGenerate/qdbusxml2cpp/org.deepin.ds.dock.taskmanager.item.h
 *
 * qdbusxml2cpp is Copyright (C) 2017 The Qt Company Ltd.
 *
 * This is an auto-generated file.
 * Do not edit! All changes made to it will be lost.
 */

#include "./dde-shell/toolGenerate/qdbusxml2cpp/org.deepin.ds.dock.taskmanager.itemAdaptor.h"
#include <QtCore/QMetaObject>
#include <QtCore/QByteArray>
#include <QtCore/QList>
#include <QtCore/QMap>
#include <QtCore/QString>
#include <QtCore/QStringList>
#include <QtCore/QVariant>

/*
 * Implementation of adaptor class ItemAdaptor
 */

ItemAdaptor::ItemAdaptor(QObject *parent)
    : QDBusAbstractAdaptor(parent)
{
    // constructor
    setAutoRelaySignals(true);
}

ItemAdaptor::~ItemAdaptor()
{
    // destructor
}

QString ItemAdaptor::icon() const
{
    // get the value of property icon
    return qvariant_cast< QString >(parent()->property("icon"));
}

QString ItemAdaptor::id() const
{
    // get the value of property id
    return qvariant_cast< QString >(parent()->property("id"));
}

bool ItemAdaptor::isActive() const
{
    // get the value of property isActive
    return qvariant_cast< bool >(parent()->property("isActive"));
}

void ItemAdaptor::setIsActive(bool value)
{
    // set the value of property isActive
    parent()->setProperty("isActive", QVariant::fromValue(value));
}

bool ItemAdaptor::isDocked() const
{
    // get the value of property isDocked
    return qvariant_cast< bool >(parent()->property("isDocked"));
}

void ItemAdaptor::setIsDocked(bool value)
{
    // set the value of property isDocked
    parent()->setProperty("isDocked", QVariant::fromValue(value));
}

QString ItemAdaptor::menus() const
{
    // get the value of property menus
    return qvariant_cast< QString >(parent()->property("menus"));
}

QString ItemAdaptor::name() const
{
    // get the value of property name
    return qvariant_cast< QString >(parent()->property("name"));
}

void ItemAdaptor::handleClick(const QString &action)
{
    // handle method call org.deepin.ds.Dock.TaskManager.Item.handleClick
    QMetaObject::invokeMethod(parent(), "handleClick", Q_ARG(QString, action));
}

void ItemAdaptor::setDocked(bool docked)
{
    // handle method call org.deepin.ds.Dock.TaskManager.Item.setDocked
    QMetaObject::invokeMethod(parent(), "setDocked", Q_ARG(bool, docked));
}

