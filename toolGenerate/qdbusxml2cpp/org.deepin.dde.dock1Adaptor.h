/*
 * This file was generated by qdbusxml2cpp version 0.8
 * Command line was: qdbusxml2cpp ./dde-shell/panels/dock/api/old/org.deepin.dde.dock1.xml -a ./dde-shell/toolGenerate/qdbusxml2cpp/org.deepin.dde.dock1Adaptor -i ./dde-shell/toolGenerate/qdbusxml2cpp/org.deepin.dde.dock1.h
 *
 * qdbusxml2cpp is Copyright (C) 2017 The Qt Company Ltd.
 *
 * This is an auto-generated file.
 * This file may have been hand-edited. Look for HAND-EDIT comments
 * before re-generating it.
 */

#ifndef ORG_DEEPIN_DDE_DOCK1ADAPTOR_H
#define ORG_DEEPIN_DDE_DOCK1ADAPTOR_H

#include <QtCore/QObject>
#include <QtDBus/QtDBus>
#include "./dde-shell/toolGenerate/qdbusxml2cpp/org.deepin.dde.dock1.h"
QT_BEGIN_NAMESPACE
class QByteArray;
template<class T> class QList;
template<class Key, class Value> class QMap;
class QString;
class QStringList;
class QVariant;
QT_END_NAMESPACE

/*
 * Adaptor class for interface org.deepin.dde.Dock1
 */
class Dock1Adaptor: public QDBusAbstractAdaptor
{
    Q_OBJECT
    Q_CLASSINFO("D-Bus Interface", "org.deepin.dde.Dock1")
    Q_CLASSINFO("D-Bus Introspection", ""
"  <interface name=\"org.deepin.dde.Dock1\">\n"
"    <property access=\"read\" type=\"(iiii)\" name=\"geometry\">\n"
"      <annotation value=\"QRect\" name=\"org.qtproject.QtDBus.QtTypeName\"/>\n"
"    </property>\n"
"    <property access=\"readwrite\" type=\"b\" name=\"showInPrimary\"/>\n"
"    <method name=\"callShow\"/>\n"
"    <method name=\"ReloadPlugins\"/>\n"
"    <method name=\"GetLoadedPlugins\">\n"
"      <arg direction=\"out\" type=\"as\" name=\"list\"/>\n"
"    </method>\n"
"    <method name=\"plugins\">\n"
"      <arg direction=\"out\" type=\"a(sssssb)\"/>\n"
"      <annotation value=\"DockItemInfos\" name=\"org.qtproject.QtDBus.QtTypeName.Out0\"/>\n"
"    </method>\n"
"    <method name=\"resizeDock\">\n"
"      <arg direction=\"in\" type=\"i\" name=\"offset\"/>\n"
"      <arg direction=\"in\" type=\"b\" name=\"dragging\"/>\n"
"    </method>\n"
"    <method name=\"getPluginKey\">\n"
"      <arg direction=\"in\" type=\"s\" name=\"pluginName\"/>\n"
"      <arg direction=\"out\" type=\"s\" name=\"key\"/>\n"
"    </method>\n"
"    <method name=\"getPluginVisible\">\n"
"      <arg direction=\"in\" type=\"s\" name=\"pluginName\"/>\n"
"      <arg direction=\"out\" type=\"b\" name=\"visible\"/>\n"
"    </method>\n"
"    <method name=\"setPluginVisible\">\n"
"      <arg direction=\"in\" type=\"s\" name=\"pluginName\"/>\n"
"      <arg direction=\"in\" type=\"b\" name=\"visible\"/>\n"
"    </method>\n"
"    <method name=\"setItemOnDock\">\n"
"      <arg direction=\"in\" type=\"s\" name=\"settingKey\"/>\n"
"      <arg direction=\"in\" type=\"s\" name=\"itemKey\"/>\n"
"      <arg direction=\"in\" type=\"b\" name=\"visible\"/>\n"
"    </method>\n"
"    <signal name=\"pluginVisibleChanged\">\n"
"      <arg type=\"s\"/>\n"
"      <arg type=\"b\"/>\n"
"    </signal>\n"
"  </interface>\n"
        "")
public:
    Dock1Adaptor(QObject *parent);
    virtual ~Dock1Adaptor();

public: // PROPERTIES
    Q_PROPERTY(QRect geometry READ geometry)
    QRect geometry() const;

    Q_PROPERTY(bool showInPrimary READ showInPrimary WRITE setShowInPrimary)
    bool showInPrimary() const;
    void setShowInPrimary(bool value);

public Q_SLOTS: // METHODS
    QStringList GetLoadedPlugins();
    void ReloadPlugins();
    void callShow();
    QString getPluginKey(const QString &pluginName);
    bool getPluginVisible(const QString &pluginName);
    DockItemInfos plugins();
    void resizeDock(int offset, bool dragging);
    void setItemOnDock(const QString &settingKey, const QString &itemKey, bool visible);
    void setPluginVisible(const QString &pluginName, bool visible);
Q_SIGNALS: // SIGNALS
    void pluginVisibleChanged(const QString &in0, bool in1);
};

#endif
