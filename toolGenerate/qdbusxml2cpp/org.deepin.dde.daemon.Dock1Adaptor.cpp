/*
 * This file was generated by qdbusxml2cpp version 0.8
 * Source file was org.deepin.dde.daemon.Dock1.xml
 *
 * qdbusxml2cpp is Copyright (C) The Qt Company Ltd. and other contributors.
 *
 * This is an auto-generated file.
 * Do not edit! All changes made to it will be lost.
 */

#include "./toolGenerate/qdbusxml2cpp/org.deepin.dde.daemon.Dock1Adaptor.h"
#include <QtCore/QMetaObject>
#include <QtCore/QByteArray>
#include <QtCore/QList>
#include <QtCore/QMap>
#include <QtCore/QString>
#include <QtCore/QStringList>
#include <QtCore/QVariant>

/*
 * Implementation of adaptor class Dock1Adaptor
 */

Dock1Adaptor::Dock1Adaptor(QObject *parent)
    : QDBusAbstractAdaptor(parent)
{
    // constructor
    setAutoRelaySignals(true);
}

Dock1Adaptor::~Dock1Adaptor()
{
    // destructor
}

int Dock1Adaptor::displayMode() const
{
    // get the value of property DisplayMode
    return qvariant_cast< int >(parent()->property("DisplayMode"));
}

void Dock1Adaptor::setDisplayMode(int value)
{
    // set the value of property DisplayMode
    parent()->setProperty("DisplayMode", QVariant::fromValue(value));
}

QRect Dock1Adaptor::frontendWindowRect() const
{
    // get the value of property FrontendWindowRect
    return qvariant_cast< QRect >(parent()->property("FrontendWindowRect"));
}

int Dock1Adaptor::hideMode() const
{
    // get the value of property HideMode
    return qvariant_cast< int >(parent()->property("HideMode"));
}

void Dock1Adaptor::setHideMode(int value)
{
    // set the value of property HideMode
    parent()->setProperty("HideMode", QVariant::fromValue(value));
}

int Dock1Adaptor::hideState() const
{
    // get the value of property HideState
    return qvariant_cast< int >(parent()->property("HideState"));
}

bool Dock1Adaptor::locked() const
{
    // get the value of property Locked
    return qvariant_cast< bool >(parent()->property("Locked"));
}

void Dock1Adaptor::setLocked(bool value)
{
    // set the value of property Locked
    parent()->setProperty("Locked", QVariant::fromValue(value));
}

int Dock1Adaptor::position() const
{
    // get the value of property Position
    return qvariant_cast< int >(parent()->property("Position"));
}

void Dock1Adaptor::setPosition(int value)
{
    // set the value of property Position
    parent()->setProperty("Position", QVariant::fromValue(value));
}

uint Dock1Adaptor::windowSizeEfficient() const
{
    // get the value of property WindowSizeEfficient
    return qvariant_cast< uint >(parent()->property("WindowSizeEfficient"));
}

void Dock1Adaptor::setWindowSizeEfficient(uint value)
{
    // set the value of property WindowSizeEfficient
    parent()->setProperty("WindowSizeEfficient", QVariant::fromValue(value));
}

uint Dock1Adaptor::windowSizeFashion() const
{
    // get the value of property WindowSizeFashion
    return qvariant_cast< uint >(parent()->property("WindowSizeFashion"));
}

void Dock1Adaptor::setWindowSizeFashion(uint value)
{
    // set the value of property WindowSizeFashion
    parent()->setProperty("WindowSizeFashion", QVariant::fromValue(value));
}

bool Dock1Adaptor::IsDocked(const QString &desktopFile)
{
    // handle method call org.deepin.dde.daemon.Dock1.IsDocked
    bool docked;
    QMetaObject::invokeMethod(parent(), "IsDocked", Q_RETURN_ARG(bool, docked), Q_ARG(QString, desktopFile));
    return docked;
}

bool Dock1Adaptor::RequestDock(const QString &desktopFile, int index)
{
    // handle method call org.deepin.dde.daemon.Dock1.RequestDock
    bool docked;
    QMetaObject::invokeMethod(parent(), "RequestDock", Q_RETURN_ARG(bool, docked), Q_ARG(QString, desktopFile), Q_ARG(int, index));
    return docked;
}

bool Dock1Adaptor::RequestUndock(const QString &desktopFile)
{
    // handle method call org.deepin.dde.daemon.Dock1.RequestUndock
    bool docked;
    QMetaObject::invokeMethod(parent(), "RequestUndock", Q_RETURN_ARG(bool, docked), Q_ARG(QString, desktopFile));
    return docked;
}

