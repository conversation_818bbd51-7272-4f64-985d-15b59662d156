/*
 * This file was generated by qdbusxml2cpp version 0.8
 * Command line was: qdbusxml2cpp ./dde-shell/applets/dde-appearance/org.deepin.dde.Appearance1.xml -a ./dde-shell/toolGenerate/qdbusxml2cpp/org.deepin.dde.Appearance1Adaptor -i ./dde-shell/toolGenerate/qdbusxml2cpp/org.deepin.dde.Appearance1.h
 *
 * qdbusxml2cpp is Copyright (C) 2017 The Qt Company Ltd.
 *
 * This is an auto-generated file.
 * Do not edit! All changes made to it will be lost.
 */

#include "./dde-shell/toolGenerate/qdbusxml2cpp/org.deepin.dde.Appearance1Adaptor.h"
#include <QtCore/QMetaObject>
#include <QtCore/QByteArray>
#include <QtCore/QList>
#include <QtCore/QMap>
#include <QtCore/QString>
#include <QtCore/QStringList>
#include <QtCore/QVariant>

/*
 * Implementation of adaptor class Appearance1Adaptor
 */

Appearance1Adaptor::Appearance1Adaptor(QObject *parent)
    : QDBusAbstractAdaptor(parent)
{
    // constructor
    setAutoRelaySignals(true);
}

Appearance1Adaptor::~Appearance1Adaptor()
{
    // destructor
}

QString Appearance1Adaptor::background() const
{
    // get the value of property Background
    return qvariant_cast< QString >(parent()->property("Background"));
}

void Appearance1Adaptor::setBackground(const QString &value)
{
    // set the value of property Background
    parent()->setProperty("Background", QVariant::fromValue(value));
}

QString Appearance1Adaptor::cursorTheme() const
{
    // get the value of property CursorTheme
    return qvariant_cast< QString >(parent()->property("CursorTheme"));
}

void Appearance1Adaptor::setCursorTheme(const QString &value)
{
    // set the value of property CursorTheme
    parent()->setProperty("CursorTheme", QVariant::fromValue(value));
}

double Appearance1Adaptor::fontSize() const
{
    // get the value of property FontSize
    return qvariant_cast< double >(parent()->property("FontSize"));
}

void Appearance1Adaptor::setFontSize(double value)
{
    // set the value of property FontSize
    parent()->setProperty("FontSize", QVariant::fromValue(value));
}

QString Appearance1Adaptor::globalTheme() const
{
    // get the value of property GlobalTheme
    return qvariant_cast< QString >(parent()->property("GlobalTheme"));
}

QString Appearance1Adaptor::gtkTheme() const
{
    // get the value of property GtkTheme
    return qvariant_cast< QString >(parent()->property("GtkTheme"));
}

void Appearance1Adaptor::setGtkTheme(const QString &value)
{
    // set the value of property GtkTheme
    parent()->setProperty("GtkTheme", QVariant::fromValue(value));
}

QString Appearance1Adaptor::iconTheme() const
{
    // get the value of property IconTheme
    return qvariant_cast< QString >(parent()->property("IconTheme"));
}

void Appearance1Adaptor::setIconTheme(const QString &value)
{
    // set the value of property IconTheme
    parent()->setProperty("IconTheme", QVariant::fromValue(value));
}

QString Appearance1Adaptor::monospaceFont() const
{
    // get the value of property MonospaceFont
    return qvariant_cast< QString >(parent()->property("MonospaceFont"));
}

void Appearance1Adaptor::setMonospaceFont(const QString &value)
{
    // set the value of property MonospaceFont
    parent()->setProperty("MonospaceFont", QVariant::fromValue(value));
}

double Appearance1Adaptor::opacity() const
{
    // get the value of property Opacity
    return qvariant_cast< double >(parent()->property("Opacity"));
}

void Appearance1Adaptor::setOpacity(double value)
{
    // set the value of property Opacity
    parent()->setProperty("Opacity", QVariant::fromValue(value));
}

QString Appearance1Adaptor::qtActiveColor() const
{
    // get the value of property QtActiveColor
    return qvariant_cast< QString >(parent()->property("QtActiveColor"));
}

void Appearance1Adaptor::setQtActiveColor(const QString &value)
{
    // set the value of property QtActiveColor
    parent()->setProperty("QtActiveColor", QVariant::fromValue(value));
}

QString Appearance1Adaptor::standardFont() const
{
    // get the value of property StandardFont
    return qvariant_cast< QString >(parent()->property("StandardFont"));
}

void Appearance1Adaptor::setStandardFont(const QString &value)
{
    // set the value of property StandardFont
    parent()->setProperty("StandardFont", QVariant::fromValue(value));
}

QString Appearance1Adaptor::wallpaperSlideShow() const
{
    // get the value of property WallpaperSlideShow
    return qvariant_cast< QString >(parent()->property("WallpaperSlideShow"));
}

void Appearance1Adaptor::setWallpaperSlideShow(const QString &value)
{
    // set the value of property WallpaperSlideShow
    parent()->setProperty("WallpaperSlideShow", QVariant::fromValue(value));
}

void Appearance1Adaptor::Delete(const QString &in0, const QString &in1)
{
    // handle method call org.deepin.dde.Appearance1.Delete
    QMetaObject::invokeMethod(parent(), "Delete", Q_ARG(QString, in0), Q_ARG(QString, in1));
}

QString Appearance1Adaptor::GetCurrentWorkspaceBackgroundForMonitor(const QString &strMonitorName)
{
    // handle method call org.deepin.dde.Appearance1.GetCurrentWorkspaceBackgroundForMonitor
    QString uri;
    QMetaObject::invokeMethod(parent(), "GetCurrentWorkspaceBackgroundForMonitor", Q_RETURN_ARG(QString, uri), Q_ARG(QString, strMonitorName));
    return uri;
}

double Appearance1Adaptor::GetScaleFactor()
{
    // handle method call org.deepin.dde.Appearance1.GetScaleFactor
    double out0;
    QMetaObject::invokeMethod(parent(), "GetScaleFactor", Q_RETURN_ARG(double, out0));
    return out0;
}

QMap<QString,double> Appearance1Adaptor::GetScreenScaleFactors()
{
    // handle method call org.deepin.dde.Appearance1.GetScreenScaleFactors
    QMap<QString,double> scaleFactors;
    QMetaObject::invokeMethod(parent(), "GetScreenScaleFactors", Q_RETURN_ARG(QMap<QString,double>, scaleFactors));
    return scaleFactors;
}

QString Appearance1Adaptor::List(const QString &in0)
{
    // handle method call org.deepin.dde.Appearance1.List
    QString out0;
    QMetaObject::invokeMethod(parent(), "List", Q_RETURN_ARG(QString, out0), Q_ARG(QString, in0));
    return out0;
}

void Appearance1Adaptor::Set(const QString &in0, const QString &in1)
{
    // handle method call org.deepin.dde.Appearance1.Set
    QMetaObject::invokeMethod(parent(), "Set", Q_ARG(QString, in0), Q_ARG(QString, in1));
}

void Appearance1Adaptor::SetScaleFactor(double in0)
{
    // handle method call org.deepin.dde.Appearance1.SetScaleFactor
    QMetaObject::invokeMethod(parent(), "SetScaleFactor", Q_ARG(double, in0));
}

void Appearance1Adaptor::SetScreenScaleFactors(const QMap<QString,double> &scaleFactors)
{
    // handle method call org.deepin.dde.Appearance1.SetScreenScaleFactors
    QMetaObject::invokeMethod(parent(), "SetScreenScaleFactors", Q_ARG(QMap<QString,double>, scaleFactors));
}

QString Appearance1Adaptor::Show(const QString &in0, const QStringList &in1)
{
    // handle method call org.deepin.dde.Appearance1.Show
    QString out0;
    QMetaObject::invokeMethod(parent(), "Show", Q_RETURN_ARG(QString, out0), Q_ARG(QString, in0), Q_ARG(QStringList, in1));
    return out0;
}

QString Appearance1Adaptor::Thumbnail(const QString &in0, const QString &in1)
{
    // handle method call org.deepin.dde.Appearance1.Thumbnail
    QString out0;
    QMetaObject::invokeMethod(parent(), "Thumbnail", Q_RETURN_ARG(QString, out0), Q_ARG(QString, in0), Q_ARG(QString, in1));
    return out0;
}

