/*
 * This file was generated by qdbusxml2cpp version 0.8
 * Command line was: qdbusxml2cpp ./dde-shell/panels/dock/taskmanager/api/amdbus/org.desktopspec.ObjectManager1.xml -a ./dde-shell/toolGenerate/qdbusxml2cpp/org.desktopspec.ObjectManager1Adaptor -i ./dde-shell/toolGenerate/qdbusxml2cpp/org.desktopspec.ObjectManager1.h
 *
 * qdbusxml2cpp is Copyright (C) 2017 The Qt Company Ltd.
 *
 * This is an auto-generated file.
 * This file may have been hand-edited. Look for HAND-EDIT comments
 * before re-generating it.
 */

#ifndef ORG_DESKTOPSPEC_OBJECTMANAGER1ADAPTOR_H
#define ORG_DESKTOPSPEC_OBJECTMANAGER1ADAPTOR_H

#include <QtCore/QObject>
#include <QtDBus/QtDBus>
#include "./dde-shell/toolGenerate/qdbusxml2cpp/org.desktopspec.ObjectManager1.h"
QT_BEGIN_NAMESPACE
class QByteArray;
template<class T> class QList;
template<class Key, class Value> class QMap;
class QString;
class QStringList;
class QVariant;
QT_END_NAMESPACE

/*
 * Adaptor class for interface org.desktopspec.DBus.ObjectManager
 */
class ObjectManagerAdaptor: public QDBusAbstractAdaptor
{
    Q_OBJECT
    Q_CLASSINFO("D-Bus Interface", "org.desktopspec.DBus.ObjectManager")
    Q_CLASSINFO("D-Bus Introspection", ""
"  <interface name=\"org.desktopspec.DBus.ObjectManager\">\n"
"    <method name=\"GetManagedObjects\">\n"
"      <arg direction=\"out\" type=\"a{oa{sa{sv}}}\" name=\"objpath_interfaces_and_properties\"/>\n"
"      <annotation value=\"ObjectMap\" name=\"org.qtproject.QtDBus.QtTypeName.Out0\"/>\n"
"    </method>\n"
"    <signal name=\"InterfacesAdded\">\n"
"      <arg type=\"o\" name=\"object_path\"/>\n"
"      <arg type=\"a{sa{sv}}\" name=\"interfaces_and_properties\"/>\n"
"      <annotation value=\"ObjectInterfaceMap\" name=\"org.qtproject.QtDBus.QtTypeName.Out1\"/>\n"
"    </signal>\n"
"    <signal name=\"InterfacesRemoved\">\n"
"      <arg type=\"o\" name=\"object_path\"/>\n"
"      <arg type=\"as\" name=\"interfaces\"/>\n"
"    </signal>\n"
"  </interface>\n"
        "")
public:
    ObjectManagerAdaptor(QObject *parent);
    virtual ~ObjectManagerAdaptor();

public: // PROPERTIES
public Q_SLOTS: // METHODS
    ObjectMap GetManagedObjects();
Q_SIGNALS: // SIGNALS
    void InterfacesAdded(const QDBusObjectPath &object_path, ObjectInterfaceMap interfaces_and_properties);
    void InterfacesRemoved(const QDBusObjectPath &object_path, const QStringList &interfaces);
};

#endif
