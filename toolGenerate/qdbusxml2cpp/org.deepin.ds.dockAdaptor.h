/*
 * This file was generated by qdbusxml2cpp version 0.8
 * Command line was: qdbusxml2cpp ./dde-shell/panels/dock/api/dbus/org.deepin.ds.dock.xml -a ./dde-shell/toolGenerate/qdbusxml2cpp/org.deepin.ds.dockAdaptor -i ./dde-shell/toolGenerate/qdbusxml2cpp/org.deepin.ds.dock.h
 *
 * qdbusxml2cpp is Copyright (C) 2017 The Qt Company Ltd.
 *
 * This is an auto-generated file.
 * This file may have been hand-edited. Look for HAND-EDIT comments
 * before re-generating it.
 */

#ifndef ORG_DEEPIN_DS_DOCKADAPTOR_H
#define ORG_DEEPIN_DS_DOCKADAPTOR_H

#include <QtCore/QObject>
#include <QtDBus/QtDBus>
#include "./dde-shell/toolGenerate/qdbusxml2cpp/org.deepin.ds.dock.h"
QT_BEGIN_NAMESPACE
class QByteArray;
template<class T> class QList;
template<class Key, class Value> class QMap;
class QString;
class QStringList;
class QVariant;
QT_END_NAMESPACE

/*
 * Adaptor class for interface org.deepin.dde.Dock1
 */
class Dock1Adaptor: public QDBusAbstractAdaptor
{
    Q_OBJECT
    Q_CLASSINFO("D-Bus Interface", "org.deepin.dde.Dock1")
    Q_CLASSINFO("D-Bus Introspection", ""
"  <interface name=\"org.deepin.dde.Dock1\">\n"
"    <property access=\"read\" type=\"(iiii)\" name=\"geometry\">\n"
"      <annotation value=\"QRect\" name=\"org.qtproject.QtDBus.QtTypeName\"/>\n"
"    </property>\n"
"    <property access=\"readwrite\" type=\"i\" name=\"position\"/>\n"
"    <property access=\"readwrite\" type=\"b\" name=\"showInPrimary\"/>\n"
"    <method name=\"callShow\"/>\n"
"    <method name=\"ReloadPlugins\"/>\n"
"  </interface>\n"
        "")
public:
    Dock1Adaptor(QObject *parent);
    virtual ~Dock1Adaptor();

public: // PROPERTIES
    Q_PROPERTY(QRect geometry READ geometry)
    QRect geometry() const;

    Q_PROPERTY(int position READ position WRITE setPosition)
    int position() const;
    void setPosition(int value);

    Q_PROPERTY(bool showInPrimary READ showInPrimary WRITE setShowInPrimary)
    bool showInPrimary() const;
    void setShowInPrimary(bool value);

public Q_SLOTS: // METHODS
    void ReloadPlugins();
    void callShow();
Q_SIGNALS: // SIGNALS
};

#endif
