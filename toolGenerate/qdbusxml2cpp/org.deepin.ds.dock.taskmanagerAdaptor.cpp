/*
 * This file was generated by qdbusxml2cpp version 0.8
 * Command line was: qdbusxml2cpp ./dde-shell/panels/dock/taskmanager/api/dbus/org.deepin.ds.dock.taskmanager.xml -a ./dde-shell/toolGenerate/qdbusxml2cpp/org.deepin.ds.dock.taskmanagerAdaptor -i ./dde-shell/toolGenerate/qdbusxml2cpp/org.deepin.ds.dock.taskmanager.h
 *
 * qdbusxml2cpp is Copyright (C) 2017 The Qt Company Ltd.
 *
 * This is an auto-generated file.
 * Do not edit! All changes made to it will be lost.
 */

#include "./dde-shell/toolGenerate/qdbusxml2cpp/org.deepin.ds.dock.taskmanagerAdaptor.h"
#include <QtCore/QMetaObject>
#include <QtCore/QByteArray>
#include <QtCore/QList>
#include <QtCore/QMap>
#include <QtCore/QString>
#include <QtCore/QStringList>
#include <QtCore/QVariant>

/*
 * Implementation of adaptor class TaskManagerAdaptor
 */

TaskManagerAdaptor::TaskManagerAdaptor(QObject *parent)
    : QDBusAbstractAdaptor(parent)
{
    // constructor
    setAutoRelaySignals(true);
}

TaskManagerAdaptor::~TaskManagerAdaptor()
{
    // destructor
}

bool TaskManagerAdaptor::allowForceQuit() const
{
    // get the value of property allowForceQuit
    return qvariant_cast< bool >(parent()->property("allowForceQuit"));
}

void TaskManagerAdaptor::setAllowForceQuit(bool value)
{
    // set the value of property allowForceQuit
    parent()->setProperty("allowForceQuit", QVariant::fromValue(value));
}

bool TaskManagerAdaptor::windowSplit() const
{
    // get the value of property windowSplit
    return qvariant_cast< bool >(parent()->property("windowSplit"));
}

void TaskManagerAdaptor::setWindowSplit(bool value)
{
    // set the value of property windowSplit
    parent()->setProperty("windowSplit", QVariant::fromValue(value));
}

