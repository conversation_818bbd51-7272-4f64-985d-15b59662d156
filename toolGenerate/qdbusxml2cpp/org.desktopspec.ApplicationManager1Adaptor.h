/*
 * This file was generated by qdbusxml2cpp version 0.8
 * Command line was: qdbusxml2cpp ./dde-shell/panels/dock/taskmanager/api/amdbus/org.desktopspec.ApplicationManager1.xml -a ./dde-shell/toolGenerate/qdbusxml2cpp/org.desktopspec.ApplicationManager1Adaptor -i ./dde-shell/toolGenerate/qdbusxml2cpp/org.desktopspec.ApplicationManager1.h
 *
 * qdbusxml2cpp is Copyright (C) 2017 The Qt Company Ltd.
 *
 * This is an auto-generated file.
 * This file may have been hand-edited. Look for HAND-EDIT comments
 * before re-generating it.
 */

#ifndef ORG_DESKTOPSPEC_APPLICATIONMANAGER1ADAPTOR_H
#define ORG_DESKTOPSPEC_APPLICATIONMANAGER1ADAPTOR_H

#include <QtCore/QObject>
#include <QtDBus/QtDBus>
#include "./dde-shell/toolGenerate/qdbusxml2cpp/org.desktopspec.ApplicationManager1.h"
QT_BEGIN_NAMESPACE
class QByteArray;
template<class T> class QList;
template<class Key, class Value> class QMap;
class QString;
class QStringList;
class QVariant;
QT_END_NAMESPACE

/*
 * Adaptor class for interface org.desktopspec.ApplicationManager1
 */
class ApplicationManager1Adaptor: public QDBusAbstractAdaptor
{
    Q_OBJECT
    Q_CLASSINFO("D-Bus Interface", "org.desktopspec.ApplicationManager1")
    Q_CLASSINFO("D-Bus Introspection", ""
"  <interface name=\"org.desktopspec.ApplicationManager1\">\n"
"    <method name=\"Identify\">\n"
"      <arg direction=\"in\" type=\"h\" name=\"pidfd\"/>\n"
"      <arg direction=\"out\" type=\"s\" name=\"id\"/>\n"
"      <arg direction=\"out\" type=\"o\" name=\"instance\"/>\n"
"      <arg direction=\"out\" type=\"a{sa{sv}}\" name=\"application_instance_info\"/>\n"
"      <annotation value=\"ObjectInterfaceMap\" name=\"org.qtproject.QtDBus.QtTypeName.Out2\"/>\n"
"      <annotation value=\"Given a pidfd,                        this method return a destkop file id,                        an application instance object path,                        as well as an application object path.                         NOTE:                        1. You should use pidfd_open(2) to get a pidfd.\" name=\"org.freedesktop.DBus.Description\"/>\n"
"    </method>\n"
"  </interface>\n"
        "")
public:
    ApplicationManager1Adaptor(QObject *parent);
    virtual ~ApplicationManager1Adaptor();

public: // PROPERTIES
public Q_SLOTS: // METHODS
    QString Identify(const QDBusUnixFileDescriptor &pidfd, QDBusObjectPath &instance, ObjectInterfaceMap &application_instance_info);
Q_SIGNALS: // SIGNALS
};

#endif
