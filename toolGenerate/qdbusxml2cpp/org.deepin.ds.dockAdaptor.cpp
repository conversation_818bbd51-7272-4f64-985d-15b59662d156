/*
 * This file was generated by qdbusxml2cpp version 0.8
 * Command line was: qdbusxml2cpp ./dde-shell/panels/dock/api/dbus/org.deepin.ds.dock.xml -a ./dde-shell/toolGenerate/qdbusxml2cpp/org.deepin.ds.dockAdaptor -i ./dde-shell/toolGenerate/qdbusxml2cpp/org.deepin.ds.dock.h
 *
 * qdbusxml2cpp is Copyright (C) 2017 The Qt Company Ltd.
 *
 * This is an auto-generated file.
 * Do not edit! All changes made to it will be lost.
 */

#include "./dde-shell/toolGenerate/qdbusxml2cpp/org.deepin.ds.dockAdaptor.h"
#include <QtCore/QMetaObject>
#include <QtCore/QByteArray>
#include <QtCore/QList>
#include <QtCore/QMap>
#include <QtCore/QString>
#include <QtCore/QStringList>
#include <QtCore/QVariant>

/*
 * Implementation of adaptor class Dock1Adaptor
 */

Dock1Adaptor::Dock1Adaptor(QObject *parent)
    : QDBusAbstractAdaptor(parent)
{
    // constructor
    setAutoRelaySignals(true);
}

Dock1Adaptor::~Dock1Adaptor()
{
    // destructor
}

QRect Dock1Adaptor::geometry() const
{
    // get the value of property geometry
    return qvariant_cast< QRect >(parent()->property("geometry"));
}

int Dock1Adaptor::position() const
{
    // get the value of property position
    return qvariant_cast< int >(parent()->property("position"));
}

void Dock1Adaptor::setPosition(int value)
{
    // set the value of property position
    parent()->setProperty("position", QVariant::fromValue(value));
}

bool Dock1Adaptor::showInPrimary() const
{
    // get the value of property showInPrimary
    return qvariant_cast< bool >(parent()->property("showInPrimary"));
}

void Dock1Adaptor::setShowInPrimary(bool value)
{
    // set the value of property showInPrimary
    parent()->setProperty("showInPrimary", QVariant::fromValue(value));
}

void Dock1Adaptor::ReloadPlugins()
{
    // handle method call org.deepin.dde.Dock1.ReloadPlugins
    QMetaObject::invokeMethod(parent(), "ReloadPlugins");
}

void Dock1Adaptor::callShow()
{
    // handle method call org.deepin.dde.Dock1.callShow
    QMetaObject::invokeMethod(parent(), "callShow");
}

