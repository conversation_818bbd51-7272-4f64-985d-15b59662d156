/*
 * This file was generated by qdbusxml2cpp version 0.8
 * Command line was: qdbusxml2cpp ./dde-shell/panels/dock/taskmanager/api/amdbus/org.desktopspec.ApplicationManager1.xml -a ./dde-shell/toolGenerate/qdbusxml2cpp/org.desktopspec.ApplicationManager1Adaptor -i ./dde-shell/toolGenerate/qdbusxml2cpp/org.desktopspec.ApplicationManager1.h
 *
 * qdbusxml2cpp is Copyright (C) 2017 The Qt Company Ltd.
 *
 * This is an auto-generated file.
 * Do not edit! All changes made to it will be lost.
 */

#include "./dde-shell/toolGenerate/qdbusxml2cpp/org.desktopspec.ApplicationManager1Adaptor.h"
#include <QtCore/QMetaObject>
#include <QtCore/QByteArray>
#include <QtCore/QList>
#include <QtCore/QMap>
#include <QtCore/QString>
#include <QtCore/QStringList>
#include <QtCore/QVariant>

/*
 * Implementation of adaptor class ApplicationManager1Adaptor
 */

ApplicationManager1Adaptor::ApplicationManager1Adaptor(QObject *parent)
    : QDBusAbstractAdaptor(parent)
{
    // constructor
    setAutoRelaySignals(true);
}

ApplicationManager1Adaptor::~ApplicationManager1Adaptor()
{
    // destructor
}

QString ApplicationManager1Adaptor::Identify(const QDBusUnixFileDescriptor &pidfd, QDBusObjectPath &instance, ObjectInterfaceMap &application_instance_info)
{
    // handle method call org.desktopspec.ApplicationManager1.Identify
    //return static_cast<YourObjectType *>(parent())->Identify(pidfd, instance, application_instance_info);
}

