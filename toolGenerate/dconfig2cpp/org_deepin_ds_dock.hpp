/**
 * This file is generated by dconfig2cpp.
 * Command line arguments: ./dconfig2cpp -p ./dde-shell/toolGenerate/dconfig2cpp ./dde-shell/panels/dock/dconfig/org.deepin.ds.dock.json
 * Generation time: 2025-01-14T10:55:02
 * JSON file version: 1.0
 * 
 * WARNING: DO NOT MODIFY THIS FILE MANUALLY.
 * If you need to change the content, please modify the dconfig2cpp tool.
 */

#ifndef ORG_DEEPIN_DS_DOCK_H
#define ORG_DEEPIN_DS_DOCK_H

#include <QThread>
#include <QVariant>
#include <QDebug>
#include <QAtomicPointer>
#include <QAtomicInteger>
#include <DConfig>

class org_deepin_ds_dock : public QObject {
    Q_OBJECT

    Q_PROPERTY(double Dock_Size READ Dock_Size WRITE setDock_Size NOTIFY Dock_SizeChanged)
    Q_PROPERTY(QString Hide_Mode READ Hide_Mode WRITE setHide_Mode NOTIFY Hide_ModeChanged)
    Q_PROPERTY(QString Indicator_Style READ Indicator_Style WRITE setIndicator_Style NOTIFY Indicator_StyleChanged)
    Q_PROPERTY(QString Item_Alignment READ Item_Alignment WRITE setItem_Alignment NOTIFY Item_AlignmentChanged)
    Q_PROPERTY(QVariantMap Plugins_Visible READ Plugins_Visible WRITE setPlugins_Visible NOTIFY Plugins_VisibleChanged)
    Q_PROPERTY(QString Position READ Position WRITE setPosition NOTIFY PositionChanged)
    Q_PROPERTY(bool Show_In_Primary READ Show_In_Primary WRITE setShow_In_Primary NOTIFY Show_In_PrimaryChanged)
public:
    explicit org_deepin_ds_dock(QThread *thread, const QString &appId, const QString &name, const QString &subpath, QObject *parent = nullptr)
        : QObject(parent) {

        if (!thread->isRunning()) {
            qWarning() << QStringLiteral("Warning: The provided thread is not running.");
        }
        Q_ASSERT(QThread::currentThread() != thread);
        auto worker = new QObject();
        worker->moveToThread(thread);
        QMetaObject::invokeMethod(worker, [=]() {
            auto config = DTK_CORE_NAMESPACE::DConfig::create(appId, name, subpath, nullptr);
            if (!config) {
                qWarning() << QStringLiteral("Failed to create DConfig instance.");
                worker->deleteLater();
                return;
            }
            config->moveToThread(QThread::currentThread());
            initialize(config);
            worker->deleteLater();
        });
    }
    explicit org_deepin_ds_dock(QThread *thread, DTK_CORE_NAMESPACE::DConfigBackend *backend, const QString &appId, const QString &name, const QString &subpath, QObject *parent = nullptr)
        : QObject(parent) {

        if (!thread->isRunning()) {
            qWarning() << QStringLiteral("Warning: The provided thread is not running.");
        }
        Q_ASSERT(QThread::currentThread() != thread);
        auto worker = new QObject();
        worker->moveToThread(thread);
        QMetaObject::invokeMethod(worker, [=]() {
            auto config = DTK_CORE_NAMESPACE::DConfig::create(backend, appId, name, subpath, nullptr);
            if (!config) {
                qWarning() << QStringLiteral("Failed to create DConfig instance.");
                worker->deleteLater();
                return;
            }
            config->moveToThread(QThread::currentThread());
            initialize(config);
            worker->deleteLater();
        });
    }
    explicit org_deepin_ds_dock(QThread *thread, const QString &name, const QString &subpath, QObject *parent = nullptr)
        : QObject(parent) {

        if (!thread->isRunning()) {
            qWarning() << QStringLiteral("Warning: The provided thread is not running.");
        }
        Q_ASSERT(QThread::currentThread() != thread);
        auto worker = new QObject();
        worker->moveToThread(thread);
        QMetaObject::invokeMethod(worker, [=]() {
            auto config = DTK_CORE_NAMESPACE::DConfig::create(name, subpath, nullptr);
            if (!config) {
                qWarning() << QStringLiteral("Failed to create DConfig instance.");
                worker->deleteLater();
                return;
            }
            config->moveToThread(QThread::currentThread());
            initialize(config);
            worker->deleteLater();
        });
    }
    explicit org_deepin_ds_dock(QThread *thread, DTK_CORE_NAMESPACE::DConfigBackend *backend, const QString &name, const QString &subpath, QObject *parent = nullptr)
        : QObject(parent) {

        if (!thread->isRunning()) {
            qWarning() << QStringLiteral("Warning: The provided thread is not running.");
        }
        Q_ASSERT(QThread::currentThread() != thread);
        auto worker = new QObject();
        worker->moveToThread(thread);
        QMetaObject::invokeMethod(worker, [=]() {
            auto config = DTK_CORE_NAMESPACE::DConfig::create(backend, name, subpath, nullptr);
            if (!config) {
                qWarning() << QStringLiteral("Failed to create DConfig instance.");
                worker->deleteLater();
                return;
            }
            config->moveToThread(QThread::currentThread());
            initialize(config);
            worker->deleteLater();
        });
    }
    ~org_deepin_ds_dock() {
        if (m_config.loadRelaxed()) {
            m_config.loadRelaxed()->deleteLater();
        }
    }

    double Dock_Size() const {
        return p_Dock_Size;
    }
    void setDock_Size(const double &value) {
        auto oldValue = p_Dock_Size;
        p_Dock_Size = value;
        markPropertySet(0);
        if (auto config = m_config.loadRelaxed()) {
            QMetaObject::invokeMethod(config, [this, value]() {
                m_config.loadRelaxed()->setValue(QStringLiteral("Dock_Size"), value);
            });
        }
        if (p_Dock_Size != oldValue) {
            Q_EMIT Dock_SizeChanged();
        }
    }
    QString Hide_Mode() const {
        return p_Hide_Mode;
    }
    void setHide_Mode(const QString &value) {
        auto oldValue = p_Hide_Mode;
        p_Hide_Mode = value;
        markPropertySet(1);
        if (auto config = m_config.loadRelaxed()) {
            QMetaObject::invokeMethod(config, [this, value]() {
                m_config.loadRelaxed()->setValue(QStringLiteral("Hide_Mode"), value);
            });
        }
        if (p_Hide_Mode != oldValue) {
            Q_EMIT Hide_ModeChanged();
        }
    }
    QString Indicator_Style() const {
        return p_Indicator_Style;
    }
    void setIndicator_Style(const QString &value) {
        auto oldValue = p_Indicator_Style;
        p_Indicator_Style = value;
        markPropertySet(2);
        if (auto config = m_config.loadRelaxed()) {
            QMetaObject::invokeMethod(config, [this, value]() {
                m_config.loadRelaxed()->setValue(QStringLiteral("Indicator_Style"), value);
            });
        }
        if (p_Indicator_Style != oldValue) {
            Q_EMIT Indicator_StyleChanged();
        }
    }
    QString Item_Alignment() const {
        return p_Item_Alignment;
    }
    void setItem_Alignment(const QString &value) {
        auto oldValue = p_Item_Alignment;
        p_Item_Alignment = value;
        markPropertySet(3);
        if (auto config = m_config.loadRelaxed()) {
            QMetaObject::invokeMethod(config, [this, value]() {
                m_config.loadRelaxed()->setValue(QStringLiteral("Item_Alignment"), value);
            });
        }
        if (p_Item_Alignment != oldValue) {
            Q_EMIT Item_AlignmentChanged();
        }
    }
    QVariantMap Plugins_Visible() const {
        return p_Plugins_Visible;
    }
    void setPlugins_Visible(const QVariantMap &value) {
        auto oldValue = p_Plugins_Visible;
        p_Plugins_Visible = value;
        markPropertySet(4);
        if (auto config = m_config.loadRelaxed()) {
            QMetaObject::invokeMethod(config, [this, value]() {
                m_config.loadRelaxed()->setValue(QStringLiteral("Plugins_Visible"), value);
            });
        }
        if (p_Plugins_Visible != oldValue) {
            Q_EMIT Plugins_VisibleChanged();
        }
    }
    QString Position() const {
        return p_Position;
    }
    void setPosition(const QString &value) {
        auto oldValue = p_Position;
        p_Position = value;
        markPropertySet(5);
        if (auto config = m_config.loadRelaxed()) {
            QMetaObject::invokeMethod(config, [this, value]() {
                m_config.loadRelaxed()->setValue(QStringLiteral("Position"), value);
            });
        }
        if (p_Position != oldValue) {
            Q_EMIT PositionChanged();
        }
    }
    bool Show_In_Primary() const {
        return p_Show_In_Primary;
    }
    void setShow_In_Primary(const bool &value) {
        auto oldValue = p_Show_In_Primary;
        p_Show_In_Primary = value;
        markPropertySet(6);
        if (auto config = m_config.loadRelaxed()) {
            QMetaObject::invokeMethod(config, [this, value]() {
                m_config.loadRelaxed()->setValue(QStringLiteral("Show_In_Primary"), value);
            });
        }
        if (p_Show_In_Primary != oldValue) {
            Q_EMIT Show_In_PrimaryChanged();
        }
    }
Q_SIGNALS:
    void Dock_SizeChanged();
    void Hide_ModeChanged();
    void Indicator_StyleChanged();
    void Item_AlignmentChanged();
    void Plugins_VisibleChanged();
    void PositionChanged();
    void Show_In_PrimaryChanged();
private:
    void initialize(DTK_CORE_NAMESPACE::DConfig *config) {
        Q_ASSERT(!m_config.loadRelaxed());
        m_config.storeRelaxed(config);
        if (testPropertySet(0)) {
            config->setValue(QStringLiteral("Dock_Size"), QVariant::fromValue(p_Dock_Size));
        } else {
            updateValue(QStringLiteral("Dock_Size"), QVariant::fromValue(p_Dock_Size));
        }
        if (testPropertySet(1)) {
            config->setValue(QStringLiteral("Hide_Mode"), QVariant::fromValue(p_Hide_Mode));
        } else {
            updateValue(QStringLiteral("Hide_Mode"), QVariant::fromValue(p_Hide_Mode));
        }
        if (testPropertySet(2)) {
            config->setValue(QStringLiteral("Indicator_Style"), QVariant::fromValue(p_Indicator_Style));
        } else {
            updateValue(QStringLiteral("Indicator_Style"), QVariant::fromValue(p_Indicator_Style));
        }
        if (testPropertySet(3)) {
            config->setValue(QStringLiteral("Item_Alignment"), QVariant::fromValue(p_Item_Alignment));
        } else {
            updateValue(QStringLiteral("Item_Alignment"), QVariant::fromValue(p_Item_Alignment));
        }
        if (testPropertySet(4)) {
            config->setValue(QStringLiteral("Plugins_Visible"), QVariant::fromValue(p_Plugins_Visible));
        } else {
            updateValue(QStringLiteral("Plugins_Visible"), QVariant::fromValue(p_Plugins_Visible));
        }
        if (testPropertySet(5)) {
            config->setValue(QStringLiteral("Position"), QVariant::fromValue(p_Position));
        } else {
            updateValue(QStringLiteral("Position"), QVariant::fromValue(p_Position));
        }
        if (testPropertySet(6)) {
            config->setValue(QStringLiteral("Show_In_Primary"), QVariant::fromValue(p_Show_In_Primary));
        } else {
            updateValue(QStringLiteral("Show_In_Primary"), QVariant::fromValue(p_Show_In_Primary));
        }

        connect(config, &DTK_CORE_NAMESPACE::DConfig::valueChanged, this, [this](const QString &key) {
            updateValue(key);
        }, Qt::DirectConnection);
    }
    void updateValue(const QString &key, const QVariant &fallback = QVariant()) {
        Q_ASSERT(QThread::currentThread() == m_config.loadRelaxed()->thread());
        const QVariant &value = m_config.loadRelaxed()->value(key, fallback);
        if (key == QStringLiteral("Dock_Size")) {
            auto newValue = qvariant_cast<double>(value);
            QMetaObject::invokeMethod(this, [this, newValue]() {
                if (p_Dock_Size != newValue) {
                    p_Dock_Size = newValue;
                    Q_EMIT Dock_SizeChanged();
                }
            });
            return;
        }
        if (key == QStringLiteral("Hide_Mode")) {
            auto newValue = qvariant_cast<QString>(value);
            QMetaObject::invokeMethod(this, [this, newValue]() {
                if (p_Hide_Mode != newValue) {
                    p_Hide_Mode = newValue;
                    Q_EMIT Hide_ModeChanged();
                }
            });
            return;
        }
        if (key == QStringLiteral("Indicator_Style")) {
            auto newValue = qvariant_cast<QString>(value);
            QMetaObject::invokeMethod(this, [this, newValue]() {
                if (p_Indicator_Style != newValue) {
                    p_Indicator_Style = newValue;
                    Q_EMIT Indicator_StyleChanged();
                }
            });
            return;
        }
        if (key == QStringLiteral("Item_Alignment")) {
            auto newValue = qvariant_cast<QString>(value);
            QMetaObject::invokeMethod(this, [this, newValue]() {
                if (p_Item_Alignment != newValue) {
                    p_Item_Alignment = newValue;
                    Q_EMIT Item_AlignmentChanged();
                }
            });
            return;
        }
        if (key == QStringLiteral("Plugins_Visible")) {
            auto newValue = qvariant_cast<QVariantMap>(value);
            QMetaObject::invokeMethod(this, [this, newValue]() {
                if (p_Plugins_Visible != newValue) {
                    p_Plugins_Visible = newValue;
                    Q_EMIT Plugins_VisibleChanged();
                }
            });
            return;
        }
        if (key == QStringLiteral("Position")) {
            auto newValue = qvariant_cast<QString>(value);
            QMetaObject::invokeMethod(this, [this, newValue]() {
                if (p_Position != newValue) {
                    p_Position = newValue;
                    Q_EMIT PositionChanged();
                }
            });
            return;
        }
        if (key == QStringLiteral("Show_In_Primary")) {
            auto newValue = qvariant_cast<bool>(value);
            QMetaObject::invokeMethod(this, [this, newValue]() {
                if (p_Show_In_Primary != newValue) {
                    p_Show_In_Primary = newValue;
                    Q_EMIT Show_In_PrimaryChanged();
                }
            });
            return;
        }
    }
    inline void markPropertySet(const int index) {
        if (index < 32) {
            m_propertySetStatus0.fetchAndOrOrdered(1 << (index - 0));
            return;
        }
        Q_UNREACHABLE();
    }
    inline bool testPropertySet(const int index) const {
        if (index < 32) {
            return (m_propertySetStatus0.loadRelaxed() & (1 << (index - 0)));
        }
        Q_UNREACHABLE();
    }
    QAtomicPointer<DTK_CORE_NAMESPACE::DConfig> m_config = nullptr;
    double p_Dock_Size { 48 };
    QString p_Hide_Mode { QStringLiteral("keep-showing") };
    QString p_Indicator_Style { QStringLiteral("Fashion") };
    QString p_Item_Alignment { QStringLiteral("center") };
    QVariantMap p_Plugins_Visible { QVariantMap{} };
    QString p_Position { QStringLiteral("bottom") };
    bool p_Show_In_Primary { true };
    QAtomicInteger<quint32> m_propertySetStatus0 = 0;
};

#endif // ORG_DEEPIN_DS_DOCK_H
