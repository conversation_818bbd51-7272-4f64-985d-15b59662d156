/**
 * This file is generated by dconfig2cpp.
 * Command line arguments: ./dconfig2cpp -p ./dde-shell/toolGenerate/dconfig2cpp ./dde-shell/panels/notification/server/configs/org.deepin.dde.shell.notification.json
 * Generation time: 2025-01-14T10:55:02
 * JSON file version: 1.0
 * 
 * WARNING: DO NOT MODIFY THIS FILE MANUALLY.
 * If you need to change the content, please modify the dconfig2cpp tool.
 */

#ifndef ORG_DEEPIN_DDE_SHELL_NOTIFICATION_H
#define ORG_DEEPIN_DDE_SHELL_NOTIFICATION_H

#include <QThread>
#include <QVariant>
#include <QDebug>
#include <QAtomicPointer>
#include <QAtomicInteger>
#include <DConfig>

class org_deepin_dde_shell_notification : public QObject {
    Q_OBJECT

    Q_PROPERTY(QVariantMap AppNamesMap READ AppNamesMap WRITE setAppNamesMap NOTIFY AppNamesMapChanged)
    Q_PROPERTY(QVariantMap appsInfo READ appsInfo WRITE setAppsInfo NOTIFY appsInfoChanged)
    Q_PROPERTY(double bubbleContentRowCount READ bubbleContentRowCount WRITE setBubbleContentRowCount NOTIFY bubbleContentRowCountChanged)
    Q_PROPERTY(double bubbleCount READ bubbleCount WRITE setBubbleCount NOTIFY bubbleCountChanged)
    Q_PROPERTY(bool dndMode READ dndMode WRITE setDndMode NOTIFY dndModeChanged)
    Q_PROPERTY(QString endTime READ endTime WRITE setEndTime NOTIFY endTimeChanged)
    Q_PROPERTY(bool lockScreenOpenDndMode READ lockScreenOpenDndMode WRITE setLockScreenOpenDndMode NOTIFY lockScreenOpenDndModeChanged)
    Q_PROPERTY(double maxCount READ maxCount WRITE setMaxCount NOTIFY maxCountChanged)
    Q_PROPERTY(bool notificationClosed READ notificationClosed WRITE setNotificationClosed NOTIFY notificationClosedChanged)
    Q_PROPERTY(bool openByTimeInterval READ openByTimeInterval WRITE setOpenByTimeInterval NOTIFY openByTimeIntervalChanged)
    Q_PROPERTY(QList<QVariant> pinnedApps READ pinnedApps WRITE setPinnedApps NOTIFY pinnedAppsChanged)
    Q_PROPERTY(QString startTime READ startTime WRITE setStartTime NOTIFY startTimeChanged)
    Q_PROPERTY(QList<QVariant> systemApps READ systemApps WRITE setSystemApps NOTIFY systemAppsChanged)
public:
    explicit org_deepin_dde_shell_notification(QThread *thread, const QString &appId, const QString &name, const QString &subpath, QObject *parent = nullptr)
        : QObject(parent) {

        if (!thread->isRunning()) {
            qWarning() << QStringLiteral("Warning: The provided thread is not running.");
        }
        Q_ASSERT(QThread::currentThread() != thread);
        auto worker = new QObject();
        worker->moveToThread(thread);
        QMetaObject::invokeMethod(worker, [=]() {
            auto config = DTK_CORE_NAMESPACE::DConfig::create(appId, name, subpath, nullptr);
            if (!config) {
                qWarning() << QStringLiteral("Failed to create DConfig instance.");
                worker->deleteLater();
                return;
            }
            config->moveToThread(QThread::currentThread());
            initialize(config);
            worker->deleteLater();
        });
    }
    explicit org_deepin_dde_shell_notification(QThread *thread, DTK_CORE_NAMESPACE::DConfigBackend *backend, const QString &appId, const QString &name, const QString &subpath, QObject *parent = nullptr)
        : QObject(parent) {

        if (!thread->isRunning()) {
            qWarning() << QStringLiteral("Warning: The provided thread is not running.");
        }
        Q_ASSERT(QThread::currentThread() != thread);
        auto worker = new QObject();
        worker->moveToThread(thread);
        QMetaObject::invokeMethod(worker, [=]() {
            auto config = DTK_CORE_NAMESPACE::DConfig::create(backend, appId, name, subpath, nullptr);
            if (!config) {
                qWarning() << QStringLiteral("Failed to create DConfig instance.");
                worker->deleteLater();
                return;
            }
            config->moveToThread(QThread::currentThread());
            initialize(config);
            worker->deleteLater();
        });
    }
    explicit org_deepin_dde_shell_notification(QThread *thread, const QString &name, const QString &subpath, QObject *parent = nullptr)
        : QObject(parent) {

        if (!thread->isRunning()) {
            qWarning() << QStringLiteral("Warning: The provided thread is not running.");
        }
        Q_ASSERT(QThread::currentThread() != thread);
        auto worker = new QObject();
        worker->moveToThread(thread);
        QMetaObject::invokeMethod(worker, [=]() {
            auto config = DTK_CORE_NAMESPACE::DConfig::create(name, subpath, nullptr);
            if (!config) {
                qWarning() << QStringLiteral("Failed to create DConfig instance.");
                worker->deleteLater();
                return;
            }
            config->moveToThread(QThread::currentThread());
            initialize(config);
            worker->deleteLater();
        });
    }
    explicit org_deepin_dde_shell_notification(QThread *thread, DTK_CORE_NAMESPACE::DConfigBackend *backend, const QString &name, const QString &subpath, QObject *parent = nullptr)
        : QObject(parent) {

        if (!thread->isRunning()) {
            qWarning() << QStringLiteral("Warning: The provided thread is not running.");
        }
        Q_ASSERT(QThread::currentThread() != thread);
        auto worker = new QObject();
        worker->moveToThread(thread);
        QMetaObject::invokeMethod(worker, [=]() {
            auto config = DTK_CORE_NAMESPACE::DConfig::create(backend, name, subpath, nullptr);
            if (!config) {
                qWarning() << QStringLiteral("Failed to create DConfig instance.");
                worker->deleteLater();
                return;
            }
            config->moveToThread(QThread::currentThread());
            initialize(config);
            worker->deleteLater();
        });
    }
    ~org_deepin_dde_shell_notification() {
        if (m_config.loadRelaxed()) {
            m_config.loadRelaxed()->deleteLater();
        }
    }

    QVariantMap AppNamesMap() const {
        return p_AppNamesMap;
    }
    void setAppNamesMap(const QVariantMap &value) {
        auto oldValue = p_AppNamesMap;
        p_AppNamesMap = value;
        markPropertySet(0);
        if (auto config = m_config.loadRelaxed()) {
            QMetaObject::invokeMethod(config, [this, value]() {
                m_config.loadRelaxed()->setValue(QStringLiteral("AppNamesMap"), value);
            });
        }
        if (p_AppNamesMap != oldValue) {
            Q_EMIT AppNamesMapChanged();
        }
    }
    QVariantMap appsInfo() const {
        return p_appsInfo;
    }
    void setAppsInfo(const QVariantMap &value) {
        auto oldValue = p_appsInfo;
        p_appsInfo = value;
        markPropertySet(1);
        if (auto config = m_config.loadRelaxed()) {
            QMetaObject::invokeMethod(config, [this, value]() {
                m_config.loadRelaxed()->setValue(QStringLiteral("appsInfo"), value);
            });
        }
        if (p_appsInfo != oldValue) {
            Q_EMIT appsInfoChanged();
        }
    }
    double bubbleContentRowCount() const {
        return p_bubbleContentRowCount;
    }
    void setBubbleContentRowCount(const double &value) {
        auto oldValue = p_bubbleContentRowCount;
        p_bubbleContentRowCount = value;
        markPropertySet(2);
        if (auto config = m_config.loadRelaxed()) {
            QMetaObject::invokeMethod(config, [this, value]() {
                m_config.loadRelaxed()->setValue(QStringLiteral("bubbleContentRowCount"), value);
            });
        }
        if (p_bubbleContentRowCount != oldValue) {
            Q_EMIT bubbleContentRowCountChanged();
        }
    }
    double bubbleCount() const {
        return p_bubbleCount;
    }
    void setBubbleCount(const double &value) {
        auto oldValue = p_bubbleCount;
        p_bubbleCount = value;
        markPropertySet(3);
        if (auto config = m_config.loadRelaxed()) {
            QMetaObject::invokeMethod(config, [this, value]() {
                m_config.loadRelaxed()->setValue(QStringLiteral("bubbleCount"), value);
            });
        }
        if (p_bubbleCount != oldValue) {
            Q_EMIT bubbleCountChanged();
        }
    }
    bool dndMode() const {
        return p_dndMode;
    }
    void setDndMode(const bool &value) {
        auto oldValue = p_dndMode;
        p_dndMode = value;
        markPropertySet(4);
        if (auto config = m_config.loadRelaxed()) {
            QMetaObject::invokeMethod(config, [this, value]() {
                m_config.loadRelaxed()->setValue(QStringLiteral("dndMode"), value);
            });
        }
        if (p_dndMode != oldValue) {
            Q_EMIT dndModeChanged();
        }
    }
    QString endTime() const {
        return p_endTime;
    }
    void setEndTime(const QString &value) {
        auto oldValue = p_endTime;
        p_endTime = value;
        markPropertySet(5);
        if (auto config = m_config.loadRelaxed()) {
            QMetaObject::invokeMethod(config, [this, value]() {
                m_config.loadRelaxed()->setValue(QStringLiteral("endTime"), value);
            });
        }
        if (p_endTime != oldValue) {
            Q_EMIT endTimeChanged();
        }
    }
    bool lockScreenOpenDndMode() const {
        return p_lockScreenOpenDndMode;
    }
    void setLockScreenOpenDndMode(const bool &value) {
        auto oldValue = p_lockScreenOpenDndMode;
        p_lockScreenOpenDndMode = value;
        markPropertySet(6);
        if (auto config = m_config.loadRelaxed()) {
            QMetaObject::invokeMethod(config, [this, value]() {
                m_config.loadRelaxed()->setValue(QStringLiteral("lockScreenOpenDndMode"), value);
            });
        }
        if (p_lockScreenOpenDndMode != oldValue) {
            Q_EMIT lockScreenOpenDndModeChanged();
        }
    }
    double maxCount() const {
        return p_maxCount;
    }
    void setMaxCount(const double &value) {
        auto oldValue = p_maxCount;
        p_maxCount = value;
        markPropertySet(7);
        if (auto config = m_config.loadRelaxed()) {
            QMetaObject::invokeMethod(config, [this, value]() {
                m_config.loadRelaxed()->setValue(QStringLiteral("maxCount"), value);
            });
        }
        if (p_maxCount != oldValue) {
            Q_EMIT maxCountChanged();
        }
    }
    bool notificationClosed() const {
        return p_notificationClosed;
    }
    void setNotificationClosed(const bool &value) {
        auto oldValue = p_notificationClosed;
        p_notificationClosed = value;
        markPropertySet(8);
        if (auto config = m_config.loadRelaxed()) {
            QMetaObject::invokeMethod(config, [this, value]() {
                m_config.loadRelaxed()->setValue(QStringLiteral("notificationClosed"), value);
            });
        }
        if (p_notificationClosed != oldValue) {
            Q_EMIT notificationClosedChanged();
        }
    }
    bool openByTimeInterval() const {
        return p_openByTimeInterval;
    }
    void setOpenByTimeInterval(const bool &value) {
        auto oldValue = p_openByTimeInterval;
        p_openByTimeInterval = value;
        markPropertySet(9);
        if (auto config = m_config.loadRelaxed()) {
            QMetaObject::invokeMethod(config, [this, value]() {
                m_config.loadRelaxed()->setValue(QStringLiteral("openByTimeInterval"), value);
            });
        }
        if (p_openByTimeInterval != oldValue) {
            Q_EMIT openByTimeIntervalChanged();
        }
    }
    QList<QVariant> pinnedApps() const {
        return p_pinnedApps;
    }
    void setPinnedApps(const QList<QVariant> &value) {
        auto oldValue = p_pinnedApps;
        p_pinnedApps = value;
        markPropertySet(10);
        if (auto config = m_config.loadRelaxed()) {
            QMetaObject::invokeMethod(config, [this, value]() {
                m_config.loadRelaxed()->setValue(QStringLiteral("pinnedApps"), value);
            });
        }
        if (p_pinnedApps != oldValue) {
            Q_EMIT pinnedAppsChanged();
        }
    }
    QString startTime() const {
        return p_startTime;
    }
    void setStartTime(const QString &value) {
        auto oldValue = p_startTime;
        p_startTime = value;
        markPropertySet(11);
        if (auto config = m_config.loadRelaxed()) {
            QMetaObject::invokeMethod(config, [this, value]() {
                m_config.loadRelaxed()->setValue(QStringLiteral("startTime"), value);
            });
        }
        if (p_startTime != oldValue) {
            Q_EMIT startTimeChanged();
        }
    }
    QList<QVariant> systemApps() const {
        return p_systemApps;
    }
    void setSystemApps(const QList<QVariant> &value) {
        auto oldValue = p_systemApps;
        p_systemApps = value;
        markPropertySet(12);
        if (auto config = m_config.loadRelaxed()) {
            QMetaObject::invokeMethod(config, [this, value]() {
                m_config.loadRelaxed()->setValue(QStringLiteral("systemApps"), value);
            });
        }
        if (p_systemApps != oldValue) {
            Q_EMIT systemAppsChanged();
        }
    }
Q_SIGNALS:
    void AppNamesMapChanged();
    void appsInfoChanged();
    void bubbleContentRowCountChanged();
    void bubbleCountChanged();
    void dndModeChanged();
    void endTimeChanged();
    void lockScreenOpenDndModeChanged();
    void maxCountChanged();
    void notificationClosedChanged();
    void openByTimeIntervalChanged();
    void pinnedAppsChanged();
    void startTimeChanged();
    void systemAppsChanged();
private:
    void initialize(DTK_CORE_NAMESPACE::DConfig *config) {
        Q_ASSERT(!m_config.loadRelaxed());
        m_config.storeRelaxed(config);
        if (testPropertySet(0)) {
            config->setValue(QStringLiteral("AppNamesMap"), QVariant::fromValue(p_AppNamesMap));
        } else {
            updateValue(QStringLiteral("AppNamesMap"), QVariant::fromValue(p_AppNamesMap));
        }
        if (testPropertySet(1)) {
            config->setValue(QStringLiteral("appsInfo"), QVariant::fromValue(p_appsInfo));
        } else {
            updateValue(QStringLiteral("appsInfo"), QVariant::fromValue(p_appsInfo));
        }
        if (testPropertySet(2)) {
            config->setValue(QStringLiteral("bubbleContentRowCount"), QVariant::fromValue(p_bubbleContentRowCount));
        } else {
            updateValue(QStringLiteral("bubbleContentRowCount"), QVariant::fromValue(p_bubbleContentRowCount));
        }
        if (testPropertySet(3)) {
            config->setValue(QStringLiteral("bubbleCount"), QVariant::fromValue(p_bubbleCount));
        } else {
            updateValue(QStringLiteral("bubbleCount"), QVariant::fromValue(p_bubbleCount));
        }
        if (testPropertySet(4)) {
            config->setValue(QStringLiteral("dndMode"), QVariant::fromValue(p_dndMode));
        } else {
            updateValue(QStringLiteral("dndMode"), QVariant::fromValue(p_dndMode));
        }
        if (testPropertySet(5)) {
            config->setValue(QStringLiteral("endTime"), QVariant::fromValue(p_endTime));
        } else {
            updateValue(QStringLiteral("endTime"), QVariant::fromValue(p_endTime));
        }
        if (testPropertySet(6)) {
            config->setValue(QStringLiteral("lockScreenOpenDndMode"), QVariant::fromValue(p_lockScreenOpenDndMode));
        } else {
            updateValue(QStringLiteral("lockScreenOpenDndMode"), QVariant::fromValue(p_lockScreenOpenDndMode));
        }
        if (testPropertySet(7)) {
            config->setValue(QStringLiteral("maxCount"), QVariant::fromValue(p_maxCount));
        } else {
            updateValue(QStringLiteral("maxCount"), QVariant::fromValue(p_maxCount));
        }
        if (testPropertySet(8)) {
            config->setValue(QStringLiteral("notificationClosed"), QVariant::fromValue(p_notificationClosed));
        } else {
            updateValue(QStringLiteral("notificationClosed"), QVariant::fromValue(p_notificationClosed));
        }
        if (testPropertySet(9)) {
            config->setValue(QStringLiteral("openByTimeInterval"), QVariant::fromValue(p_openByTimeInterval));
        } else {
            updateValue(QStringLiteral("openByTimeInterval"), QVariant::fromValue(p_openByTimeInterval));
        }
        if (testPropertySet(10)) {
            config->setValue(QStringLiteral("pinnedApps"), QVariant::fromValue(p_pinnedApps));
        } else {
            updateValue(QStringLiteral("pinnedApps"), QVariant::fromValue(p_pinnedApps));
        }
        if (testPropertySet(11)) {
            config->setValue(QStringLiteral("startTime"), QVariant::fromValue(p_startTime));
        } else {
            updateValue(QStringLiteral("startTime"), QVariant::fromValue(p_startTime));
        }
        if (testPropertySet(12)) {
            config->setValue(QStringLiteral("systemApps"), QVariant::fromValue(p_systemApps));
        } else {
            updateValue(QStringLiteral("systemApps"), QVariant::fromValue(p_systemApps));
        }

        connect(config, &DTK_CORE_NAMESPACE::DConfig::valueChanged, this, [this](const QString &key) {
            updateValue(key);
        }, Qt::DirectConnection);
    }
    void updateValue(const QString &key, const QVariant &fallback = QVariant()) {
        Q_ASSERT(QThread::currentThread() == m_config.loadRelaxed()->thread());
        const QVariant &value = m_config.loadRelaxed()->value(key, fallback);
        if (key == QStringLiteral("AppNamesMap")) {
            auto newValue = qvariant_cast<QVariantMap>(value);
            QMetaObject::invokeMethod(this, [this, newValue]() {
                if (p_AppNamesMap != newValue) {
                    p_AppNamesMap = newValue;
                    Q_EMIT AppNamesMapChanged();
                }
            });
            return;
        }
        if (key == QStringLiteral("appsInfo")) {
            auto newValue = qvariant_cast<QVariantMap>(value);
            QMetaObject::invokeMethod(this, [this, newValue]() {
                if (p_appsInfo != newValue) {
                    p_appsInfo = newValue;
                    Q_EMIT appsInfoChanged();
                }
            });
            return;
        }
        if (key == QStringLiteral("bubbleContentRowCount")) {
            auto newValue = qvariant_cast<double>(value);
            QMetaObject::invokeMethod(this, [this, newValue]() {
                if (p_bubbleContentRowCount != newValue) {
                    p_bubbleContentRowCount = newValue;
                    Q_EMIT bubbleContentRowCountChanged();
                }
            });
            return;
        }
        if (key == QStringLiteral("bubbleCount")) {
            auto newValue = qvariant_cast<double>(value);
            QMetaObject::invokeMethod(this, [this, newValue]() {
                if (p_bubbleCount != newValue) {
                    p_bubbleCount = newValue;
                    Q_EMIT bubbleCountChanged();
                }
            });
            return;
        }
        if (key == QStringLiteral("dndMode")) {
            auto newValue = qvariant_cast<bool>(value);
            QMetaObject::invokeMethod(this, [this, newValue]() {
                if (p_dndMode != newValue) {
                    p_dndMode = newValue;
                    Q_EMIT dndModeChanged();
                }
            });
            return;
        }
        if (key == QStringLiteral("endTime")) {
            auto newValue = qvariant_cast<QString>(value);
            QMetaObject::invokeMethod(this, [this, newValue]() {
                if (p_endTime != newValue) {
                    p_endTime = newValue;
                    Q_EMIT endTimeChanged();
                }
            });
            return;
        }
        if (key == QStringLiteral("lockScreenOpenDndMode")) {
            auto newValue = qvariant_cast<bool>(value);
            QMetaObject::invokeMethod(this, [this, newValue]() {
                if (p_lockScreenOpenDndMode != newValue) {
                    p_lockScreenOpenDndMode = newValue;
                    Q_EMIT lockScreenOpenDndModeChanged();
                }
            });
            return;
        }
        if (key == QStringLiteral("maxCount")) {
            auto newValue = qvariant_cast<double>(value);
            QMetaObject::invokeMethod(this, [this, newValue]() {
                if (p_maxCount != newValue) {
                    p_maxCount = newValue;
                    Q_EMIT maxCountChanged();
                }
            });
            return;
        }
        if (key == QStringLiteral("notificationClosed")) {
            auto newValue = qvariant_cast<bool>(value);
            QMetaObject::invokeMethod(this, [this, newValue]() {
                if (p_notificationClosed != newValue) {
                    p_notificationClosed = newValue;
                    Q_EMIT notificationClosedChanged();
                }
            });
            return;
        }
        if (key == QStringLiteral("openByTimeInterval")) {
            auto newValue = qvariant_cast<bool>(value);
            QMetaObject::invokeMethod(this, [this, newValue]() {
                if (p_openByTimeInterval != newValue) {
                    p_openByTimeInterval = newValue;
                    Q_EMIT openByTimeIntervalChanged();
                }
            });
            return;
        }
        if (key == QStringLiteral("pinnedApps")) {
            auto newValue = qvariant_cast<QList<QVariant>>(value);
            QMetaObject::invokeMethod(this, [this, newValue]() {
                if (p_pinnedApps != newValue) {
                    p_pinnedApps = newValue;
                    Q_EMIT pinnedAppsChanged();
                }
            });
            return;
        }
        if (key == QStringLiteral("startTime")) {
            auto newValue = qvariant_cast<QString>(value);
            QMetaObject::invokeMethod(this, [this, newValue]() {
                if (p_startTime != newValue) {
                    p_startTime = newValue;
                    Q_EMIT startTimeChanged();
                }
            });
            return;
        }
        if (key == QStringLiteral("systemApps")) {
            auto newValue = qvariant_cast<QList<QVariant>>(value);
            QMetaObject::invokeMethod(this, [this, newValue]() {
                if (p_systemApps != newValue) {
                    p_systemApps = newValue;
                    Q_EMIT systemAppsChanged();
                }
            });
            return;
        }
    }
    inline void markPropertySet(const int index) {
        if (index < 32) {
            m_propertySetStatus0.fetchAndOrOrdered(1 << (index - 0));
            return;
        }
        Q_UNREACHABLE();
    }
    inline bool testPropertySet(const int index) const {
        if (index < 32) {
            return (m_propertySetStatus0.loadRelaxed() & (1 << (index - 0)));
        }
        Q_UNREACHABLE();
    }
    QAtomicPointer<DTK_CORE_NAMESPACE::DConfig> m_config = nullptr;
    QVariantMap p_AppNamesMap { QVariantMap{{QStringLiteral("dde-control-center"), QVariant(QStringLiteral("org.deepin.dde.control-center"))}} };
    QVariantMap p_appsInfo { QVariantMap{} };
    double p_bubbleContentRowCount { 6 };
    double p_bubbleCount { 3 };
    bool p_dndMode { false };
    QString p_endTime { QStringLiteral("07:00") };
    bool p_lockScreenOpenDndMode { false };
    double p_maxCount { 2000 };
    bool p_notificationClosed { false };
    bool p_openByTimeInterval { false };
    QList<QVariant> p_pinnedApps { QList<QVariant>{} };
    QString p_startTime { QStringLiteral("22:00") };
    QList<QVariant> p_systemApps { QList<QVariant>{QVariant(QStringLiteral("org.deepin.dde.control-center"))} };
    QAtomicInteger<quint32> m_propertySetStatus0 = 0;
};

#endif // ORG_DEEPIN_DDE_SHELL_NOTIFICATION_H
