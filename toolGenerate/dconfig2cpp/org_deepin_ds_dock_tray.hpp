/**
 * This file is generated by dconfig2cpp.
 * Command line arguments: ./dconfig2cpp -p ./dde-shell/toolGenerate/dconfig2cpp ./dde-shell/panels/dock/dconfig/org.deepin.ds.dock.tray.json
 * Generation time: 2025-01-14T10:55:02
 * JSON file version: 1.0
 * 
 * WARNING: DO NOT MODIFY THIS FILE MANUALLY.
 * If you need to change the content, please modify the dconfig2cpp tool.
 */

#ifndef ORG_DEEPIN_DS_DOCK_TRAY_H
#define ORG_DEEPIN_DS_DOCK_TRAY_H

#include <QThread>
#include <QVariant>
#include <QDebug>
#include <QAtomicPointer>
#include <QAtomicInteger>
#include <DConfig>

class org_deepin_ds_dock_tray : public QObject {
    Q_OBJECT

    Q_PROPERTY(QList<QVariant> collapsableSurfaceIds READ collapsableSurfaceIds WRITE setCollapsableSurfaceIds NOTIFY collapsableSurfaceIdsChanged)
    Q_PROPERTY(QList<QVariant> crashProneTrayPlugins READ crashProneTrayPlugins WRITE setCrashProneTrayPlugins NOTIFY crashProneTrayPluginsChanged)
    Q_PROPERTY(QList<QVariant> hiddenSurfaceIds READ hiddenSurfaceIds WRITE setHiddenSurfaceIds NOTIFY hiddenSurfaceIdsChanged)
    Q_PROPERTY(bool isCollapsed READ isCollapsed WRITE setIsCollapsed NOTIFY isCollapsedChanged)
    Q_PROPERTY(QList<QVariant> pinnedSurfaceIds READ pinnedSurfaceIds WRITE setPinnedSurfaceIds NOTIFY pinnedSurfaceIdsChanged)
    Q_PROPERTY(QList<QVariant> quickPlugins READ quickPlugins WRITE setQuickPlugins NOTIFY quickPluginsChanged)
    Q_PROPERTY(QList<QVariant> selfMaintenanceTrayPlugins READ selfMaintenanceTrayPlugins WRITE setSelfMaintenanceTrayPlugins NOTIFY selfMaintenanceTrayPluginsChanged)
    Q_PROPERTY(QList<QVariant> stashedSurfaceIds READ stashedSurfaceIds WRITE setStashedSurfaceIds NOTIFY stashedSurfaceIdsChanged)
    Q_PROPERTY(QList<QVariant> subprojectTrayPlugins READ subprojectTrayPlugins WRITE setSubprojectTrayPlugins NOTIFY subprojectTrayPluginsChanged)
public:
    explicit org_deepin_ds_dock_tray(QThread *thread, const QString &appId, const QString &name, const QString &subpath, QObject *parent = nullptr)
        : QObject(parent) {

        if (!thread->isRunning()) {
            qWarning() << QStringLiteral("Warning: The provided thread is not running.");
        }
        Q_ASSERT(QThread::currentThread() != thread);
        auto worker = new QObject();
        worker->moveToThread(thread);
        QMetaObject::invokeMethod(worker, [=]() {
            auto config = DTK_CORE_NAMESPACE::DConfig::create(appId, name, subpath, nullptr);
            if (!config) {
                qWarning() << QStringLiteral("Failed to create DConfig instance.");
                worker->deleteLater();
                return;
            }
            config->moveToThread(QThread::currentThread());
            initialize(config);
            worker->deleteLater();
        });
    }
    explicit org_deepin_ds_dock_tray(QThread *thread, DTK_CORE_NAMESPACE::DConfigBackend *backend, const QString &appId, const QString &name, const QString &subpath, QObject *parent = nullptr)
        : QObject(parent) {

        if (!thread->isRunning()) {
            qWarning() << QStringLiteral("Warning: The provided thread is not running.");
        }
        Q_ASSERT(QThread::currentThread() != thread);
        auto worker = new QObject();
        worker->moveToThread(thread);
        QMetaObject::invokeMethod(worker, [=]() {
            auto config = DTK_CORE_NAMESPACE::DConfig::create(backend, appId, name, subpath, nullptr);
            if (!config) {
                qWarning() << QStringLiteral("Failed to create DConfig instance.");
                worker->deleteLater();
                return;
            }
            config->moveToThread(QThread::currentThread());
            initialize(config);
            worker->deleteLater();
        });
    }
    explicit org_deepin_ds_dock_tray(QThread *thread, const QString &name, const QString &subpath, QObject *parent = nullptr)
        : QObject(parent) {

        if (!thread->isRunning()) {
            qWarning() << QStringLiteral("Warning: The provided thread is not running.");
        }
        Q_ASSERT(QThread::currentThread() != thread);
        auto worker = new QObject();
        worker->moveToThread(thread);
        QMetaObject::invokeMethod(worker, [=]() {
            auto config = DTK_CORE_NAMESPACE::DConfig::create(name, subpath, nullptr);
            if (!config) {
                qWarning() << QStringLiteral("Failed to create DConfig instance.");
                worker->deleteLater();
                return;
            }
            config->moveToThread(QThread::currentThread());
            initialize(config);
            worker->deleteLater();
        });
    }
    explicit org_deepin_ds_dock_tray(QThread *thread, DTK_CORE_NAMESPACE::DConfigBackend *backend, const QString &name, const QString &subpath, QObject *parent = nullptr)
        : QObject(parent) {

        if (!thread->isRunning()) {
            qWarning() << QStringLiteral("Warning: The provided thread is not running.");
        }
        Q_ASSERT(QThread::currentThread() != thread);
        auto worker = new QObject();
        worker->moveToThread(thread);
        QMetaObject::invokeMethod(worker, [=]() {
            auto config = DTK_CORE_NAMESPACE::DConfig::create(backend, name, subpath, nullptr);
            if (!config) {
                qWarning() << QStringLiteral("Failed to create DConfig instance.");
                worker->deleteLater();
                return;
            }
            config->moveToThread(QThread::currentThread());
            initialize(config);
            worker->deleteLater();
        });
    }
    ~org_deepin_ds_dock_tray() {
        if (m_config.loadRelaxed()) {
            m_config.loadRelaxed()->deleteLater();
        }
    }

    QList<QVariant> collapsableSurfaceIds() const {
        return p_collapsableSurfaceIds;
    }
    void setCollapsableSurfaceIds(const QList<QVariant> &value) {
        auto oldValue = p_collapsableSurfaceIds;
        p_collapsableSurfaceIds = value;
        markPropertySet(0);
        if (auto config = m_config.loadRelaxed()) {
            QMetaObject::invokeMethod(config, [this, value]() {
                m_config.loadRelaxed()->setValue(QStringLiteral("collapsableSurfaceIds"), value);
            });
        }
        if (p_collapsableSurfaceIds != oldValue) {
            Q_EMIT collapsableSurfaceIdsChanged();
        }
    }
    QList<QVariant> crashProneTrayPlugins() const {
        return p_crashProneTrayPlugins;
    }
    void setCrashProneTrayPlugins(const QList<QVariant> &value) {
        auto oldValue = p_crashProneTrayPlugins;
        p_crashProneTrayPlugins = value;
        markPropertySet(1);
        if (auto config = m_config.loadRelaxed()) {
            QMetaObject::invokeMethod(config, [this, value]() {
                m_config.loadRelaxed()->setValue(QStringLiteral("crashProneTrayPlugins"), value);
            });
        }
        if (p_crashProneTrayPlugins != oldValue) {
            Q_EMIT crashProneTrayPluginsChanged();
        }
    }
    QList<QVariant> hiddenSurfaceIds() const {
        return p_hiddenSurfaceIds;
    }
    void setHiddenSurfaceIds(const QList<QVariant> &value) {
        auto oldValue = p_hiddenSurfaceIds;
        p_hiddenSurfaceIds = value;
        markPropertySet(2);
        if (auto config = m_config.loadRelaxed()) {
            QMetaObject::invokeMethod(config, [this, value]() {
                m_config.loadRelaxed()->setValue(QStringLiteral("hiddenSurfaceIds"), value);
            });
        }
        if (p_hiddenSurfaceIds != oldValue) {
            Q_EMIT hiddenSurfaceIdsChanged();
        }
    }
    bool isCollapsed() const {
        return p_isCollapsed;
    }
    void setIsCollapsed(const bool &value) {
        auto oldValue = p_isCollapsed;
        p_isCollapsed = value;
        markPropertySet(3);
        if (auto config = m_config.loadRelaxed()) {
            QMetaObject::invokeMethod(config, [this, value]() {
                m_config.loadRelaxed()->setValue(QStringLiteral("isCollapsed"), value);
            });
        }
        if (p_isCollapsed != oldValue) {
            Q_EMIT isCollapsedChanged();
        }
    }
    QList<QVariant> pinnedSurfaceIds() const {
        return p_pinnedSurfaceIds;
    }
    void setPinnedSurfaceIds(const QList<QVariant> &value) {
        auto oldValue = p_pinnedSurfaceIds;
        p_pinnedSurfaceIds = value;
        markPropertySet(4);
        if (auto config = m_config.loadRelaxed()) {
            QMetaObject::invokeMethod(config, [this, value]() {
                m_config.loadRelaxed()->setValue(QStringLiteral("pinnedSurfaceIds"), value);
            });
        }
        if (p_pinnedSurfaceIds != oldValue) {
            Q_EMIT pinnedSurfaceIdsChanged();
        }
    }
    QList<QVariant> quickPlugins() const {
        return p_quickPlugins;
    }
    void setQuickPlugins(const QList<QVariant> &value) {
        auto oldValue = p_quickPlugins;
        p_quickPlugins = value;
        markPropertySet(5);
        if (auto config = m_config.loadRelaxed()) {
            QMetaObject::invokeMethod(config, [this, value]() {
                m_config.loadRelaxed()->setValue(QStringLiteral("quickPlugins"), value);
            });
        }
        if (p_quickPlugins != oldValue) {
            Q_EMIT quickPluginsChanged();
        }
    }
    QList<QVariant> selfMaintenanceTrayPlugins() const {
        return p_selfMaintenanceTrayPlugins;
    }
    void setSelfMaintenanceTrayPlugins(const QList<QVariant> &value) {
        auto oldValue = p_selfMaintenanceTrayPlugins;
        p_selfMaintenanceTrayPlugins = value;
        markPropertySet(6);
        if (auto config = m_config.loadRelaxed()) {
            QMetaObject::invokeMethod(config, [this, value]() {
                m_config.loadRelaxed()->setValue(QStringLiteral("selfMaintenanceTrayPlugins"), value);
            });
        }
        if (p_selfMaintenanceTrayPlugins != oldValue) {
            Q_EMIT selfMaintenanceTrayPluginsChanged();
        }
    }
    QList<QVariant> stashedSurfaceIds() const {
        return p_stashedSurfaceIds;
    }
    void setStashedSurfaceIds(const QList<QVariant> &value) {
        auto oldValue = p_stashedSurfaceIds;
        p_stashedSurfaceIds = value;
        markPropertySet(7);
        if (auto config = m_config.loadRelaxed()) {
            QMetaObject::invokeMethod(config, [this, value]() {
                m_config.loadRelaxed()->setValue(QStringLiteral("stashedSurfaceIds"), value);
            });
        }
        if (p_stashedSurfaceIds != oldValue) {
            Q_EMIT stashedSurfaceIdsChanged();
        }
    }
    QList<QVariant> subprojectTrayPlugins() const {
        return p_subprojectTrayPlugins;
    }
    void setSubprojectTrayPlugins(const QList<QVariant> &value) {
        auto oldValue = p_subprojectTrayPlugins;
        p_subprojectTrayPlugins = value;
        markPropertySet(8);
        if (auto config = m_config.loadRelaxed()) {
            QMetaObject::invokeMethod(config, [this, value]() {
                m_config.loadRelaxed()->setValue(QStringLiteral("subprojectTrayPlugins"), value);
            });
        }
        if (p_subprojectTrayPlugins != oldValue) {
            Q_EMIT subprojectTrayPluginsChanged();
        }
    }
Q_SIGNALS:
    void collapsableSurfaceIdsChanged();
    void crashProneTrayPluginsChanged();
    void hiddenSurfaceIdsChanged();
    void isCollapsedChanged();
    void pinnedSurfaceIdsChanged();
    void quickPluginsChanged();
    void selfMaintenanceTrayPluginsChanged();
    void stashedSurfaceIdsChanged();
    void subprojectTrayPluginsChanged();
private:
    void initialize(DTK_CORE_NAMESPACE::DConfig *config) {
        Q_ASSERT(!m_config.loadRelaxed());
        m_config.storeRelaxed(config);
        if (testPropertySet(0)) {
            config->setValue(QStringLiteral("collapsableSurfaceIds"), QVariant::fromValue(p_collapsableSurfaceIds));
        } else {
            updateValue(QStringLiteral("collapsableSurfaceIds"), QVariant::fromValue(p_collapsableSurfaceIds));
        }
        if (testPropertySet(1)) {
            config->setValue(QStringLiteral("crashProneTrayPlugins"), QVariant::fromValue(p_crashProneTrayPlugins));
        } else {
            updateValue(QStringLiteral("crashProneTrayPlugins"), QVariant::fromValue(p_crashProneTrayPlugins));
        }
        if (testPropertySet(2)) {
            config->setValue(QStringLiteral("hiddenSurfaceIds"), QVariant::fromValue(p_hiddenSurfaceIds));
        } else {
            updateValue(QStringLiteral("hiddenSurfaceIds"), QVariant::fromValue(p_hiddenSurfaceIds));
        }
        if (testPropertySet(3)) {
            config->setValue(QStringLiteral("isCollapsed"), QVariant::fromValue(p_isCollapsed));
        } else {
            updateValue(QStringLiteral("isCollapsed"), QVariant::fromValue(p_isCollapsed));
        }
        if (testPropertySet(4)) {
            config->setValue(QStringLiteral("pinnedSurfaceIds"), QVariant::fromValue(p_pinnedSurfaceIds));
        } else {
            updateValue(QStringLiteral("pinnedSurfaceIds"), QVariant::fromValue(p_pinnedSurfaceIds));
        }
        if (testPropertySet(5)) {
            config->setValue(QStringLiteral("quickPlugins"), QVariant::fromValue(p_quickPlugins));
        } else {
            updateValue(QStringLiteral("quickPlugins"), QVariant::fromValue(p_quickPlugins));
        }
        if (testPropertySet(6)) {
            config->setValue(QStringLiteral("selfMaintenanceTrayPlugins"), QVariant::fromValue(p_selfMaintenanceTrayPlugins));
        } else {
            updateValue(QStringLiteral("selfMaintenanceTrayPlugins"), QVariant::fromValue(p_selfMaintenanceTrayPlugins));
        }
        if (testPropertySet(7)) {
            config->setValue(QStringLiteral("stashedSurfaceIds"), QVariant::fromValue(p_stashedSurfaceIds));
        } else {
            updateValue(QStringLiteral("stashedSurfaceIds"), QVariant::fromValue(p_stashedSurfaceIds));
        }
        if (testPropertySet(8)) {
            config->setValue(QStringLiteral("subprojectTrayPlugins"), QVariant::fromValue(p_subprojectTrayPlugins));
        } else {
            updateValue(QStringLiteral("subprojectTrayPlugins"), QVariant::fromValue(p_subprojectTrayPlugins));
        }

        connect(config, &DTK_CORE_NAMESPACE::DConfig::valueChanged, this, [this](const QString &key) {
            updateValue(key);
        }, Qt::DirectConnection);
    }
    void updateValue(const QString &key, const QVariant &fallback = QVariant()) {
        Q_ASSERT(QThread::currentThread() == m_config.loadRelaxed()->thread());
        const QVariant &value = m_config.loadRelaxed()->value(key, fallback);
        if (key == QStringLiteral("collapsableSurfaceIds")) {
            auto newValue = qvariant_cast<QList<QVariant>>(value);
            QMetaObject::invokeMethod(this, [this, newValue]() {
                if (p_collapsableSurfaceIds != newValue) {
                    p_collapsableSurfaceIds = newValue;
                    Q_EMIT collapsableSurfaceIdsChanged();
                }
            });
            return;
        }
        if (key == QStringLiteral("crashProneTrayPlugins")) {
            auto newValue = qvariant_cast<QList<QVariant>>(value);
            QMetaObject::invokeMethod(this, [this, newValue]() {
                if (p_crashProneTrayPlugins != newValue) {
                    p_crashProneTrayPlugins = newValue;
                    Q_EMIT crashProneTrayPluginsChanged();
                }
            });
            return;
        }
        if (key == QStringLiteral("hiddenSurfaceIds")) {
            auto newValue = qvariant_cast<QList<QVariant>>(value);
            QMetaObject::invokeMethod(this, [this, newValue]() {
                if (p_hiddenSurfaceIds != newValue) {
                    p_hiddenSurfaceIds = newValue;
                    Q_EMIT hiddenSurfaceIdsChanged();
                }
            });
            return;
        }
        if (key == QStringLiteral("isCollapsed")) {
            auto newValue = qvariant_cast<bool>(value);
            QMetaObject::invokeMethod(this, [this, newValue]() {
                if (p_isCollapsed != newValue) {
                    p_isCollapsed = newValue;
                    Q_EMIT isCollapsedChanged();
                }
            });
            return;
        }
        if (key == QStringLiteral("pinnedSurfaceIds")) {
            auto newValue = qvariant_cast<QList<QVariant>>(value);
            QMetaObject::invokeMethod(this, [this, newValue]() {
                if (p_pinnedSurfaceIds != newValue) {
                    p_pinnedSurfaceIds = newValue;
                    Q_EMIT pinnedSurfaceIdsChanged();
                }
            });
            return;
        }
        if (key == QStringLiteral("quickPlugins")) {
            auto newValue = qvariant_cast<QList<QVariant>>(value);
            QMetaObject::invokeMethod(this, [this, newValue]() {
                if (p_quickPlugins != newValue) {
                    p_quickPlugins = newValue;
                    Q_EMIT quickPluginsChanged();
                }
            });
            return;
        }
        if (key == QStringLiteral("selfMaintenanceTrayPlugins")) {
            auto newValue = qvariant_cast<QList<QVariant>>(value);
            QMetaObject::invokeMethod(this, [this, newValue]() {
                if (p_selfMaintenanceTrayPlugins != newValue) {
                    p_selfMaintenanceTrayPlugins = newValue;
                    Q_EMIT selfMaintenanceTrayPluginsChanged();
                }
            });
            return;
        }
        if (key == QStringLiteral("stashedSurfaceIds")) {
            auto newValue = qvariant_cast<QList<QVariant>>(value);
            QMetaObject::invokeMethod(this, [this, newValue]() {
                if (p_stashedSurfaceIds != newValue) {
                    p_stashedSurfaceIds = newValue;
                    Q_EMIT stashedSurfaceIdsChanged();
                }
            });
            return;
        }
        if (key == QStringLiteral("subprojectTrayPlugins")) {
            auto newValue = qvariant_cast<QList<QVariant>>(value);
            QMetaObject::invokeMethod(this, [this, newValue]() {
                if (p_subprojectTrayPlugins != newValue) {
                    p_subprojectTrayPlugins = newValue;
                    Q_EMIT subprojectTrayPluginsChanged();
                }
            });
            return;
        }
    }
    inline void markPropertySet(const int index) {
        if (index < 32) {
            m_propertySetStatus0.fetchAndOrOrdered(1 << (index - 0));
            return;
        }
        Q_UNREACHABLE();
    }
    inline bool testPropertySet(const int index) const {
        if (index < 32) {
            return (m_propertySetStatus0.loadRelaxed() & (1 << (index - 0)));
        }
        Q_UNREACHABLE();
    }
    QAtomicPointer<DTK_CORE_NAMESPACE::DConfig> m_config = nullptr;
    QList<QVariant> p_collapsableSurfaceIds { QList<QVariant>{QVariant(QStringLiteral("application-tray::SNI:Fcitx-0")), QVariant(QStringLiteral("bluetooth::bluetooth-item-key")), QVariant(QStringLiteral("battery::power")), QVariant(QStringLiteral("shutdown::shutdown"))} };
    QList<QVariant> p_crashProneTrayPlugins { QList<QVariant>{} };
    QList<QVariant> p_hiddenSurfaceIds { QList<QVariant>{} };
    bool p_isCollapsed { false };
    QList<QVariant> p_pinnedSurfaceIds { QList<QVariant>{QVariant(QStringLiteral("disk-mount::mount-item-key")), QVariant(QStringLiteral("uosai::uosai")), QVariant(QStringLiteral("network::network-item-key"))} };
    QList<QVariant> p_quickPlugins { QList<QVariant>{QVariant(QStringLiteral("network")), QVariant(QStringLiteral("bluetooth")), QVariant(QStringLiteral("wireless-casting")), QVariant(QStringLiteral("grand-search")), QVariant(QStringLiteral("eye-comfort-mode")), QVariant(QStringLiteral("airplane-mode")), QVariant(QStringLiteral("dnd-mode")), QVariant(QStringLiteral("shot-start-plugin")), QVariant(QStringLiteral("shot-start-record-plugin")), QVariant(QStringLiteral("clipboard")), QVariant(QStringLiteral("system-monitor")), QVariant(QStringLiteral("media")), QVariant(QStringLiteral("dde-brightness")), QVariant(QStringLiteral("sound"))} };
    QList<QVariant> p_selfMaintenanceTrayPlugins { QList<QVariant>{QVariant(QStringLiteral("libapplication-tray.so")), QVariant(QStringLiteral("libbrightness.so")), QVariant(QStringLiteral("libdatetime.so")), QVariant(QStringLiteral("libdnd-mode.so")), QVariant(QStringLiteral("libeye-comfort-mode.so")), QVariant(QStringLiteral("libmedia.so")), QVariant(QStringLiteral("libnotification.so")), QVariant(QStringLiteral("libonboard.so")), QVariant(QStringLiteral("libshutdown.so")), QVariant(QStringLiteral("libairplane-mode.so")), QVariant(QStringLiteral("libbluetooth.so")), QVariant(QStringLiteral("libdock-tray-network-plugin.so")), QVariant(QStringLiteral("libdock-wirelesscasting-plugin.so")), QVariant(QStringLiteral("libkeyboard-layout.so")), QVariant(QStringLiteral("libpower.so")), QVariant(QStringLiteral("libsound.so"))} };
    QList<QVariant> p_stashedSurfaceIds { QList<QVariant>{} };
    QList<QVariant> p_subprojectTrayPlugins { QList<QVariant>{QVariant(QStringLiteral("libdock-clipboard-plugin.so")), QVariant(QStringLiteral("libddegrandsearch_dockplugin.so")), QVariant(QStringLiteral("libdeepin-screen-recorder-plugin.so")), QVariant(QStringLiteral("libdeepin-system-monitor-plugin.so")), QVariant(QStringLiteral("libshot-start-plugin.so")), QVariant(QStringLiteral("libshot-start-record-plugin.so")), QVariant(QStringLiteral("libdde-disk-mount-plugin.so"))} };
    QAtomicInteger<quint32> m_propertySetStatus0 = 0;
};

#endif // ORG_DEEPIN_DS_DOCK_TRAY_H
