<!DOCTYPE node PUBLIC "-//freedesktop//DTD D-BUS Object Introspection 1.0//EN" "https://www.freedesktop.org/standards/dbus/1.0/introspect.dtd">
<node>
    <interface name="org.desktopspec.ApplicationManager1">
        <method name="Identify">
            <arg type="h" name="pidfd" direction="in" />

            <arg type="s" name="id" direction="out" />
            <arg type="o" name="instance" direction="out"/>
            <arg type="a{sa{sv}}" name="application_instance_info" direction="out" />
            <annotation name="org.qtproject.QtDBus.QtTypeName.Out2" value="ObjectInterfaceMap"/>
            <annotation
                name="org.freedesktop.DBus.Description"
                value="Given a pidfd,
                       this method return a destkop file id,
                       an application instance object path,
                       as well as an application object path.

                       NOTE:
                       1. You should use pidfd_open(2) to get a pidfd."
            />
        </method>
    </interface>
</node>
