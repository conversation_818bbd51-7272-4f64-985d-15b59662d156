<!DOCTYPE node PUBLIC "-//freedesktop//DTD D-BUS Object Introspection 1.0//EN" "https://www.freedesktop.org/standards/dbus/1.0/introspect.dtd">
<node>
    <interface name="org.deepin.ds.Dock.TaskManager.Item">
        <property name="isActive" type="b" access="readwrite"/>
        <property name="isDocked" type="b" access="readwrite"/>

        <property name="id" type="s" access="read"/>
        <property name="name" type="s" access="read"/>
        <property name="icon" type="s" access="read"/>
        <property name="menus" type="s" access="read"/>

        <!-- <method name="data">
            <arg type="i" name="type" direction="in" />
        </method> -->

        <method name="setDocked">
            <arg type="b" name="docked" direction="in" />
        </method>
        <method name="handleClick">
            <arg type="s" name="action" direction="in" />
        </method>
    </interface>
</node>
