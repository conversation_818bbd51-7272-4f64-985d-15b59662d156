<?xml version="1.0" ?><!DOCTYPE TS><TS version="2.1" language="de_DE">
<context>
    <name>dock::AppItem</name>
    <message>
        <source>Open</source>
        <translation><PERSON>ffnen</translation>
    </message>
    <message>
        <source>Undock</source>
        <translation>Abdocken</translation>
    </message>
    <message>
        <source>Dock</source>
        <translation>Andocken</translation>
    </message>
    <message>
        <source>Force Quit</source>
        <translation>Auschalten erzwingen</translation>
    </message>
    <message>
        <source>Close All</source>
        <translation>Alle schließen</translation>
    </message>
</context>
<context>
    <name>dock::DockGlobalElementModel</name>
    <message>
        <source>Open</source>
        <translation>Öffnen</translation>
    </message>
    <message>
        <source>Undock</source>
        <translation>Abdocken</translation>
    </message>
    <message>
        <source>Dock</source>
        <translation>Andocken</translation>
    </message>
    <message>
        <source>Force Quit</source>
        <translation>Ausschalten erzwingen</translation>
    </message>
    <message>
        <source>Close All</source>
        <translation>All<PERSON> schließen</translation>
    </message>
</context>
</TS>