<?xml version="1.0" ?><!DOCTYPE TS><TS version="2.1" language="kab">
<context>
    <name>dock::AppItem</name>
    <message>
        <source>Open</source>
        <translation><PERSON><PERSON></translation>
    </message>
    <message>
        <source>Undock</source>
        <translation>Décocher</translation>
    </message>
    <message>
        <source>Dock</source>
        <translation>Dock</translation>
    </message>
    <message>
        <source>Force Quit</source>
        <translation>Forzar la sortie</translation>
    </message>
    <message>
        <source>Close All</source>
        <translation><PERSON><PERSON> fermer</translation>
    </message>
</context>
<context>
    <name>dock::DockGlobalElementModel</name>
    <message>
        <source>Open</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <source>Undock</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <source>Dock</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <source>Force Quit</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <source>Close All</source>
        <translation type="unfinished"/>
    </message>
</context>
</TS>