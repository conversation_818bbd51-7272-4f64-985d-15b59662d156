<?xml version="1.0" ?><!DOCTYPE TS><TS version="2.1" language="ja">
<context>
    <name>dock::AppItem</name>
    <message>
        <source>Open</source>
        <translation>開く</translation>
    </message>
    <message>
        <source>Undock</source>
        <translation>ドックから削除</translation>
    </message>
    <message>
        <source>Dock</source>
        <translation>ドックに固定</translation>
    </message>
    <message>
        <source>Force Quit</source>
        <translation>強制終了</translation>
    </message>
    <message>
        <source>Close All</source>
        <translation>すべて閉じる</translation>
    </message>
</context>
<context>
    <name>dock::DockGlobalElementModel</name>
    <message>
        <source>Open</source>
        <translation>開く</translation>
    </message>
    <message>
        <source>Undock</source>
        <translation>ドックから削除</translation>
    </message>
    <message>
        <source>Dock</source>
        <translation>ドックに固定</translation>
    </message>
    <message>
        <source>Force Quit</source>
        <translation>強制終了</translation>
    </message>
    <message>
        <source>Close All</source>
        <translation>すべて閉じる</translation>
    </message>
</context>
</TS>