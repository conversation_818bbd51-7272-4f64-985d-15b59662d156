<!DOCTYPE node PUBLIC "-//freedesktop//DTD D-BUS Object Introspection 1.0//EN"
"http://www.freedesktop.org/standards/dbus/1.0/introspect.dtd">
<node>
    <interface name="org.deepin.dde.Dock1">
        <property access="read" type="(iiii)" name="geometry">
            <annotation name="org.qtproject.QtDBus.QtTypeName" value="QRect"/>
        </property>
        <property access="readwrite" type="i" name="position"/>
        <property access="readwrite" type="b" name="showInPrimary"/>
        <method name="callShow"/>
        <method name="ReloadPlugins"/>
    </interface>
</node>
