<!DOCTYPE node PUBLIC "-//freedesktop//DTD D-BUS Object Introspection 1.0//EN"
"http://www.freedesktop.org/standards/dbus/1.0/introspect.dtd">
<node>
    <interface name="org.deepin.dde.Dock1">
        <property access="read" type="(iiii)" name="geometry">
            <annotation name="org.qtproject.QtDBus.QtTypeName" value="QRect"/>
        </property>
        <property access="readwrite" type="b" name="showInPrimary"/>
        <method name="callShow"/>
        <method name="ReloadPlugins"/>
        <method name="GetLoadedPlugins">
            <arg name="list" type="as" direction="out"/>
        </method>
        <method name="plugins">
            <arg type="a(sssssb)" direction="out"/>
            <annotation value="DockItemInfos"
                        name="org.qtproject.QtDBus.QtTypeName.Out0"/>
        </method>
        <method name="resizeDock">
            <arg name="offset" type="i" direction="in"/>
            <arg name="dragging" type="b" direction="in"/>
        </method>
        <method name="getPluginKey">
            <arg name="pluginName" type="s" direction="in"/>
            <arg name="key" type="s" direction="out"/>
        </method>
        <method name="getPluginVisible">
            <arg name="pluginName" type="s" direction="in"/>
            <arg name="visible" type="b" direction="out"/>
        </method>
        <method name="setPluginVisible">
            <arg name="pluginName" type="s" direction="in"/>
            <arg name="visible" type="b" direction="in"/>
        </method>
        <method name="setItemOnDock">
            <arg name="settingKey" type="s" direction="in"/>
            <arg name="itemKey" type="s" direction="in"/>
            <arg name="visible" type="b" direction="in"/>
        </method>
        <signal name="pluginVisibleChanged">
            <arg type="s"/>
            <arg type="b"/>
        </signal>
    </interface>
</node>
