{"magic": "dsg.config.meta", "version": "1.0", "contents": {"Dock_Size": {"value": 48, "serial": 0, "flags": [], "name": "Dock_Size", "name[zh_CN]": "*****", "description": "", "permissions": "readwrite", "visibility": "private"}, "Position": {"value": "bottom", "serial": 0, "flags": [], "name": "Position", "name[zh_CN]": "*****", "description": "", "permissions": "readwrite", "visibility": "private"}, "Hide_Mode": {"value": "keep-showing", "serial": 0, "flags": [], "name": "Hide_Mode", "name[zh_CN]": "*****", "description": "The value will influence when the dock is shown or hidden.", "permissions": "readwrite", "visibility": "private"}, "Item_Alignment": {"value": "center", "serial": 0, "flags": [], "name": "Item_Alignment", "name[zh_CN]": "*****", "description": "", "permissions": "readwrite", "visibility": "private"}, "Indicator_Style": {"value": "Fashion", "serial": 0, "flags": [], "name": "Indicator_Style", "name[zh_CN]": "*****", "description": "", "permissions": "readwrite", "visibility": "private"}, "Plugins_Visible": {"value": {}, "serial": 0, "flags": [], "name": "The visibilities of plugins", "name[zh_CN]": "插件可见性", "description": "The loaded plugin which is visible when dock is started.", "permissions": "readwrite", "visibility": "private"}, "Show_In_Primary": {"value": true, "serial": 0, "flags": [], "name": "show_in_primary", "name[zh_CN]": "任务栏显示在主屏幕", "description": "show dock in primary screen", "permissions": "readwrite", "visibility": "private"}, "Locked": {"value": false, "serial": 0, "flags": [], "name": "locked", "name[zh_CN]": "禁用自由调节", "description": "lock dock to prevent dragging resize", "permissions": "readwrite", "visibility": "private"}}}