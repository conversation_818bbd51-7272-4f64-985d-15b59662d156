<?xml version="1.0" ?><!DOCTYPE TS><TS version="2.1" language="ja">
<context>
    <name>main</name>
    <message>
        <source>Indicator Style</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <source>Fashion Mode</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <source>Efficient Mode</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <source>Classic Mode</source>
        <translation>クラシック</translation>
    </message>
    <message>
        <source>Centered Mode</source>
        <translation>中央</translation>
    </message>
    <message>
        <source>Left</source>
        <translation>左</translation>
    </message>
    <message>
        <source>Position</source>
        <translation>位置</translation>
    </message>
    <message>
        <source>Mode</source>
        <translation>モード</translation>
    </message>
    <message>
        <source>Top</source>
        <translation>上</translation>
    </message>
    <message>
        <source>Bottom</source>
        <translation>下</translation>
    </message>
    <message>
        <source>Right</source>
        <translation>右</translation>
    </message>
    <message>
        <source>Status</source>
        <translation>状態</translation>
    </message>
    <message>
        <source>Keep Shown</source>
        <translation>常に表示</translation>
    </message>
    <message>
        <source>Keep Hidden</source>
        <translation>非表示</translation>
    </message>
    <message>
        <source>Smart Hide</source>
        <translation>スマートハイド</translation>
    </message>
    <message>
        <source>Dock Settings</source>
        <translation>ドックの設定</translation>
    </message>
    <message>
        <source>Lock the Dock</source>
        <translation>ドックを固定</translation>
    </message>
</context>
</TS>