// SPDX-FileCopyrightText: 2022 UnionTech Software Technology Co., Ltd.
//
// SPDX-License-Identifier: LGPL-3.0-or-later

import QtQuick 2.15
import QtQuick.Controls 2.15
import org.deepin.ds 1.0

Item {
    id: control
    visible: false
    default property alias popupContent: popup.children
    property alias popupVisible: popup.visible
    property var popupWindow: Panel.popupWindow
    property int popupX: 0
    property int popupY: 0
    property bool readyBinding: false
    property bool isClosing: false
    property bool enableActiveMonitoring: false
    property int focusLostCount: 0
    width: popup.childrenRect.width
    height: popup.childrenRect.height

    Binding {
        when: readyBinding
        target: popupWindow; property: "width"
        value: popup.width
    }
    Binding {
        when: readyBinding
        target: popupWindow; property: "height"
        value: popup.height
    }
    Binding {
        when: readyBinding
        delayed: true
        target: popupWindow; property: "xOffset"
        value: control.popupX
    }
    Binding {
        when: readyBinding
        delayed: true
        target: popupWindow; property: "yOffset"
        value: control.popupY
    }

    function open()
    {
        console.log("PanelPopup: 尝试打开弹窗")

        if (popup.visible) {
            console.log("PanelPopup: 弹窗已经可见，先关闭再打开")
            close()
            return
        }

        if (!popupWindow) {
            console.warn("PanelPopup: popupWindow 不存在，无法打开弹窗")
            return
        }

        // The popup is being displayed. If you click on other plugin at this time,
        // the popup content of the previous plugin will be displayed first,
        // and the wrong popup size will cause the window size to change and flicker.
        if (popupWindow.visible) {
            console.log("PanelPopup: 其他弹窗正在显示，先关闭其他弹窗")
            popupWindow.close()
            popupWindow.currentItem = null
        }

        readyBinding = Qt.binding(function () {
            return popupWindow && popupWindow.currentItem === control
        })

        popupWindow.currentItem = control
        console.log("PanelPopup: 设置当前项并启动定时器")
        timer.start()
    }

    Timer {
        id: timer
        interval: 10
        onTriggered: {
            if (!popupWindow) {
                console.warn("PanelPopup: 定时器触发时 popupWindow 不存在")
                return
            }

            if (!readyBinding) {
                console.warn("PanelPopup: 定时器触发时 readyBinding 为 false")
                return
            }

            console.log("PanelPopup: 显示弹窗并请求激活")
            popupWindow.show()
            popupWindow.requestActivate()

            // // 启动延迟定时器，在弹窗完全显示后启用焦点监听
            // activeMonitoringTimer.start()
        }
    }

    // Timer {
    //     id: activeMonitoringTimer
    //     interval: 100  // 延迟100ms确保弹窗完全显示
    //     onTriggered: {
    //         console.log("PanelPopup: 启用焦点监听")
    //         enableActiveMonitoring = true
    //     }
    // }

    // 防抖定时器，避免快速的焦点变化导致意外关闭
    Timer {
        id: focusDebounceTimer
        interval: 20  // 200ms防抖延迟
        onTriggered: {
            console.log("PanelPopup: 防抖定时器触发，确认失去焦点，关闭弹窗")
            control.close()
        }
    }
    function close()
    {
        console.log("PanelPopup: 尝试关闭弹窗 (isClosing: " + isClosing + ")")

        if (isClosing) {
            console.log("PanelPopup: 正在关闭中，避免重复关闭")
            return
        }

        if (!popupWindow) {
            console.warn("PanelPopup: popupWindow 不存在，无法关闭弹窗")
            return
        }

        // avoid to closing window by other PanelPopup.
        if (!readyBinding) {
            console.log("PanelPopup: readyBinding 为 false，避免被其他 PanelPopup 关闭")
            return
        }

        isClosing = true
        // 停止焦点监听
        enableActiveMonitoring = false
        // 停止防抖定时器
        focusDebounceTimer.stop()
        focusLostCount = 0
        console.log("PanelPopup: 关闭弹窗并清空当前项")
        popupWindow.close()
        popupWindow.currentItem = null
        isClosing = false
    }

    Connections {
        target: popupWindow
        function onActiveChanged()
        {
            if (!popupWindow)
                return

            // 详细的焦点调试信息
            console.log("PanelPopup: 焦点状态变化 - active: " + popupWindow.active +
                       ", readyBinding: " + readyBinding + ", focusLostCount: " + focusLostCount)

            // 只有在启用焦点监听后才处理焦点变化
            if (!enableActiveMonitoring) {
                console.log("PanelPopup: 焦点监听未启用，忽略焦点变化")
                return
            }

            // TODO why activeChanged is not emit.
            if (popupWindow && !popupWindow.active && readyBinding) {
                focusLostCount++
                console.log("PanelPopup: 弹窗失去焦点 (第" + focusLostCount + "次)，启动防抖定时器")
                // 使用防抖定时器，避免快速的焦点变化导致意外关闭
                focusDebounceTimer.restart()
            } else if (popupWindow && popupWindow.active && readyBinding) {
                // 重新获得焦点，停止关闭定时器
                focusDebounceTimer.stop()
                focusLostCount = 0
                console.log("PanelPopup: 弹窗重新获得焦点，取消关闭")
            }
        }
    }

    Item {
        id: popup
        visible: readyBinding
        width: control.width
        height: control.height
        parent: popupWindow ? popupWindow.contentItem : undefined

        onVisibleChanged: {
            if (visible) {
                console.log("PanelPopup: 弹窗内容变为可见")
                // 弹窗变为可见时，启用焦点监听
                enableActiveMonitoring = true
                console.log("PanelPopup: 弹窗可见，启用焦点监听")
            } else {
                console.log("PanelPopup: 弹窗内容变为不可见")
                // 弹窗变为不可见时，禁用焦点监听
                enableActiveMonitoring = false
                console.log("PanelPopup: 弹窗不可见，禁用焦点监听")
            }
        }

        Component.onCompleted: {
            console.log("PanelPopup: popup 组件初始化完成")
        }
    }
    Component.onDestruction: {
        console.log("PanelPopup: 组件销毁")
        if (popup.visible) {
            console.log("PanelPopup: 组件销毁时弹窗仍可见，执行关闭")
            control.close()
        }
    }
}
