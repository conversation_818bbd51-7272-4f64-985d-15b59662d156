// SPDX-FileCopyrightText: 2022 UnionTech Software Technology Co., Ltd.
//
// SPDX-License-Identifier: LGPL-3.0-or-later

import QtQuick 2.15
import QtQuick.Controls 2.15
import org.deepin.ds 1.0

Item {
    id: control
    visible: false
    default property alias popupContent: popup.children
    property alias popupVisible: popup.visible
    property var popupWindow: Panel.popupWindow
    property int popupX: 0
    property int popupY: 0
    property bool readyBinding: false
    property bool enableActiveMonitoring: false
    width: popup.childrenRect.width
    height: popup.childrenRect.height

    Binding {
        when: readyBinding
        target: popupWindow; property: "width"
        value: popup.width
    }
    Binding {
        when: readyBinding
        target: popupWindow; property: "height"
        value: popup.height
    }
    Binding {
        when: readyBinding
        delayed: true
        target: popupWindow; property: "xOffset"
        value: control.popupX
    }
    Binding {
        when: readyBinding
        delayed: true
        target: popupWindow; property: "yOffset"
        value: control.popupY
    }

    function open()
    {
        console.log("PanelPopup: 尝试打开弹窗")

        if (popup.visible) {
            console.log("PanelPopup: 弹窗已经可见，先关闭再打开")
            close()
            return
        }

        if (!popupWindow) {
            console.warn("PanelPopup: popupWindow 不存在，无法打开弹窗")
            return
        }

        // The popup is being displayed. If you click on other plugin at this time,
        // the popup content of the previous plugin will be displayed first,
        // and the wrong popup size will cause the window size to change and flicker.
        if (popupWindow.visible) {
            console.log("PanelPopup: 其他弹窗正在显示，先关闭其他弹窗")
            popupWindow.close()
            popupWindow.currentItem = null
        }

        readyBinding = Qt.binding(function () {
            return popupWindow && popupWindow.currentItem === control
        })

        popupWindow.currentItem = control
        console.log("PanelPopup: 设置当前项并启动定时器")
        timer.start()
    }

    Timer {
        id: timer
        interval: 10
        onTriggered: {
            if (!popupWindow) {
                console.warn("PanelPopup: 定时器触发时 popupWindow 不存在")
                return
            }

            if (!readyBinding) {
                console.warn("PanelPopup: 定时器触发时 readyBinding 为 false")
                return
            }

            console.log("PanelPopup: 显示弹窗并请求激活")
            popupWindow.show()
            popupWindow.requestActivate()

            // activeMonitoringTimer.start()
        }
    }


    function close()
    {
        console.log("PanelPopup: 尝试关闭弹窗 (isClosing: " + isClosing + ")")

        if (isClosing) {
            console.log("PanelPopup: 正在关闭中，避免重复关闭")
            return
        }

        if (!popupWindow) {
            console.warn("PanelPopup: popupWindow 不存在，无法关闭弹窗")
            return
        }

        // avoid to closing window by other PanelPopup.
        if (!readyBinding) {
            console.log("PanelPopup: readyBinding 为 false，避免被其他 PanelPopup 关闭")
            return
        }

        isClosing = true
        // 停止焦点监听
        enableActiveMonitoring = false
        console.log("PanelPopup: 关闭弹窗并清空当前项")
        popupWindow.close()
        popupWindow.currentItem = null
        isClosing = false
    }

    Connections {
        target: popupWindow
        function onActiveChanged()
        {
            if (!popupWindow)
                return
            // 只有在启用焦点监听后才处理焦点变化
            if (!enableActiveMonitoring) {
                return
            }

            // TODO why activeChanged is not emit.
            if (popupWindow && !popupWindow.active && readyBinding) {
                console.log("PanelPopup: 弹窗失去焦点，自动关闭")
                control.close()
            }
        }
    }

    Item {
        id: popup
        visible: readyBinding
        width: control.width
        height: control.height
        parent: popupWindow ? popupWindow.contentItem : undefined

        onVisibleChanged: {
            if (visible) {
                console.log("PanelPopup: 弹窗内容变为可见")
            } else {
                console.log("PanelPopup: 弹窗内容变为不可见")
            }
        }
    }
    Component.onCompleted: {
        console.log("PanelPopup: 主组件初始化完成，启用焦点监听")
        enableActiveMonitoring = true
    }

    Component.onDestruction: {
        if (popup.visible)
            control.close()
    }
}
